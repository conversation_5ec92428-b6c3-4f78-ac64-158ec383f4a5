# BitQuery Large Bitcoin Transactions Script

This Python script queries the BitQuery.io API to find large Bitcoin transactions based on your specified criteria.

## Features

- 🔍 Query Bitcoin transactions with high output values
- 📊 Configurable date ranges and value thresholds
- 💰 Display transaction details including USD values, fees, and block information
- 🔐 Secure API key handling via environment variables
- ⚡ Error handling and formatted output

## Setup

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set your API key (recommended):**
   ```bash
   export BITQUERY_API_KEY="your_api_key_here"
   ```
   
   Or the script will prompt you for it when running.

## Usage

### Basic Usage
```bash
python bitquery_large_bitcoin_transactions.py
```

### Customizing Parameters

You can modify the following parameters in the `main()` function:

- `start_date`: Start date for the query (YYYY-MM-DD format)
- `end_date`: End date for the query (YYYY-MM-DD format)  
- `min_value_usd`: Minimum transaction value in USD (as string)
- `limit`: Maximum number of transactions to return

### Example Output

```
🚀 Querying BitQuery for large Bitcoin transactions...
📅 Date range: 2024-01-01 to 2024-01-31
💰 Minimum value: $1,000,000 USD
📊 Limit: 20 transactions
============================================================

📊 Large Bitcoin Transactions Summary
==================================================
Total transactions found: 15

🔍 Transaction Details:
================================================================================

1. Transaction Hash: abc123...
   📅 Timestamp: 2024-01-15T10:30:45Z
   🏗️  Block Height: 825000
   💰 Output Value: $2,500,000.00 USD
   📥 Input Value: $2,500,100.00 USD
   💸 Fee: $100.00 USD
   🔢 Outputs: 2 | Inputs: 1
```

## API Key

You need a BitQuery.io API key to use this script. You can:

1. Get one from [BitQuery.io](https://bitquery.io/)
2. Set it as an environment variable: `BITQUERY_API_KEY`
3. Or enter it when prompted by the script

## Configuration

The script uses realistic default values:
- Date range: Recent month (you should update to current dates)
- Minimum value: $1,000,000 USD
- Limit: 20 transactions

## Error Handling

The script includes comprehensive error handling for:
- Network issues
- API errors
- Invalid responses
- Missing API keys

## Notes

- The original query in your example used future dates (2025-06-01 to 2025-06-10) which won't return data
- I've adjusted the default dates to a more recent period
- The minimum value is set to $1M USD for more realistic results
- You can easily modify these parameters in the script
