block_height,block_signed_at,block_hash,tx_hash,tx_offset,miner_address,from_address,to_address,value,gas_offered,gas_spent,gas_price,fees_paid,successful,chain_id,chain_name,explorers,from_address_label,to_address_label,gas_metadata,gas_quote_rate,gas_quote,pretty_gas_quote,value_quote,pretty_value_quote,log_events
22723792,2025-06-17 11:00:59+00:00,0xd91a11109856418a0c6e138aafa18bb951e53079722644d700e2368c75feca90,0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c,1,******************************************,******************************************,******************************************,305000000000000000,410946,270776,1200540085,325077442055960,True,1,eth-mainnet,"[{'label': 'Etherscan', 'url': 'https://etherscan.io/tx/0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1669653891-eth.svg'}",2554.5847964499326,0.8304378911449893,$0.83,779.1483629172294,$779.15,"[{'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 4, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************00000000043b93e2507e8000', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '305000000000000000'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 5, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000b71b4e954f132f3770d9bde0d1d5eb50c32147ef'], 'raw_log_data': '******************************************00000000043b93e2507e8000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '305000000000000000'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 6, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'ETH Chan', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'ETHCHAN', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000b71b4e954f132f3770d9bde0d1d5eb50c32147ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************000000000014287d29a0c200', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5674017568637440'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 7, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': '0x5c69bee701ef814a2b6a3edd4b1652cb9cc5aa6f', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '******************************************000000000575cedcda66ac2e00000000000000000000000000000000000000000000000128dba898694584f6', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '393447990417075246'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '21390876227680961782'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 8, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': '0x5c69bee701ef814a2b6a3edd4b1652cb9cc5aa6f', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************000000000000000000000000000000000000000000000000000000000000000000000000043b93e2507e80000000000000000000000000000000000000000000000000000014287d29a0c2000000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '305000000000000000'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5674017568637440'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 9, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'ETH Chan', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'ETHCHAN', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '******************************************000000000014287d29a0c200', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5674017568637440'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 10, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xbbb02a24579dc2e59c1609253b6ddab5457ba00895b3eda80dd41e03e2cd7e55', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '******************************************000000000000000000000000'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae0000000000000000000000005417994ae69e6ccb283bb6dbdba3006b3d3f9f95000000000000000000000000000000000000000000000000043b93e2507e80000000000000000000000000000000000000000000000000000014287d29a0c200ffffffffffffffffffffffffffffffffffffffffffffffffffff55f5ae3ce532'}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 11, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'ETH Chan', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'ETHCHAN', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x000000000000000000000000ecdcdbdc2ced1b99535a53d53b74bd8c113bac4e'], 'raw_log_data': '******************************************000000000014287d29a0c200', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5674017568637440'}]}}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 12, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0x5b720c55abcceaba7ef3716f5b928c856bfb0ff010e91bc210faae854e953260000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b7500000000000000000000000000000000000000000000000000000000000000000000000000000000000000005417994ae69e6ccb283bb6dbdba3006b3d3f9f95000000000000000000000000000000000000000000000000043b93e2507e80000000000000000000000000000000000000000000000000000014287d29a0c2000000000000000000000000000000000000000000000000000000000068514aeb'}, {'block_height': 22723792, 'block_signed_at': '2025-06-17T11:00:59Z', 'tx_offset': 1, 'tx_hash': '0xf23e313007e4ef36a07fc3569c72d52692b319b0eb499f70846a70b75198559c', 'log_offset': 13, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x38eee76fd911eabac79da7af16053e809be0e12c8637f156e77e1af309b99537', '0x5b720c55abcceaba7ef3716f5b928c856bfb0ff010e91bc210faae854e953260'], 'raw_log_data': '******************************************0000000000000000000000e00000000000000000000000000000000000000000000000000000000000000120000000000000000000000000ecdcdbdc2ced1b99535a53d53b74bd8c113bac4e00000000000000000000000000000000000000000000000000000000000000000000000000000000000000005417994ae69e6ccb283bb6dbdba3006b3d3f9f95000000000000000000000000000000000000000000000000043b93e2507e80000000000000000000000000000000000000000000000000000014287d29a0c200000000000000000000000000000000000000000000000000000000000000000d62696e616e636557616c6c657400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a30783030303030303030303030303030303030303030303030303030303030303030303030303030303000000000000000000000000000000000000000000000', 'decoded': {'name': 'LiFiGenericSwapCompleted', 'signature': 'LiFiGenericSwapCompleted(indexed bytes32 transactionId, string integrator, string referrer, address receiver, address fromAssetId, address toAssetId, uint256 fromAmount, uint256 toAmount)', 'params': [{'name': 'transactionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'W3IMVavM6rp+83FvW5KMhWv7D/AQ6RvCEPquhU6VMmA='}, {'name': 'integrator', 'type': 'string', 'indexed': False, 'decoded': True, 'value': 'binanceWallet'}, {'name': 'referrer', 'type': 'string', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'receiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'toAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '305000000000000000'}, {'name': 'toAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5674017568637440'}]}}]"
22723790,2025-06-17 11:00:35+00:00,0xefc6cd843f38b376f7962e61926cfdf67993f4f8f36864c6b9e9d40cf174af5a,0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb,91,******************************************,******************************************,******************************************,3838500714419267,300000,222266,1624948404,361170781963464,True,1,eth-mainnet,"[{'label': 'Etherscan', 'url': 'https://etherscan.io/tx/0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1669653891-eth.svg'}",2554.5847964499326,0.9226413885257987,$0.92,9.805775566217664,$9.81,"[{'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 187, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************00000000000da318978d9443', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3838500714419267'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 188, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000002e8135be71230c6b1b4045696d41c09db0414226'], 'raw_log_data': '******************************************00000000000da318978d9443', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3838500714419267'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 189, 'sender_address': '0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000002e8135be71230c6b1b4045696d41c09db0414226', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************00000000000000000095302e', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9777198'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 190, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Pancake LPs', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'Cake-LP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '******************************************000000000000002158ccfd360000000000000000000000000000000000000000000000030a709e82aa81459e', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '143223749942'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '56092507642943063454'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 191, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Pancake LPs', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'Cake-LP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************000000000000000000000000000000000000000000000000000000000000000000000000000da318978d9443000000000000000000000000000000000000000000000000000000000095302e0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3838500714419267'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9777198'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 192, 'sender_address': '0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '******************************************00000000000000000095302e', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9777198'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 193, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xbbb02a24579dc2e59c1609253b6ddab5457ba00895b3eda80dd41e03e2cd7e55', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '******************************************000000000000000000000000'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000000000000000000000000000000da318978d9443000000000000000000000000000000000000000000000000000000000095302e0000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 194, 'sender_address': '0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000000a099f602094575b5b3f1df876df379466d5a055'], 'raw_log_data': '******************************************00000000000000000095302e', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0a099f602094575b5b3f1df876df379466d5a055'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9777198'}]}}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 195, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0xf8491ea55065ab01a5aec48241a2e8b26ce0e3a7831a81ccc22bdd39e145dd97000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b750000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000000000000000000000000000000da318978d9443000000000000000000000000000000000000000000000000000000000095302e0000000000000000000000000000000000000000000000000000000068514ad3'}, {'block_height': 22723790, 'block_signed_at': '2025-06-17T11:00:35Z', 'tx_offset': 91, 'tx_hash': '0xc2072f70638e9d574384edddadfc818159c33f5dcd6a0c630c93bfb47021aefb', 'log_offset': 196, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x38eee76fd911eabac79da7af16053e809be0e12c8637f156e77e1af309b99537', '0xf8491ea55065ab01a5aec48241a2e8b26ce0e3a7831a81ccc22bdd39e145dd97'], 'raw_log_data': '******************************************0000000000000000000000e000000000000000000000000000000000000000000000000000000000000001200000000000000000000000000a099f602094575b5b3f1df876df379466d5a0550000000000000000000000000000000000000000000000000000000000000000000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48000000000000000000000000000000000000000000000000000da318978d9443000000000000000000000000000000000000000000000000000000000095302e00000000000000000000000000000000000000000000000000000000000000086c6966692d617069000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a30783030303030303030303030303030303030303030303030303030303030303030303030303030303000000000000000000000000000000000000000000000', 'decoded': {'name': 'LiFiGenericSwapCompleted', 'signature': 'LiFiGenericSwapCompleted(indexed bytes32 transactionId, string integrator, string referrer, address receiver, address fromAssetId, address toAssetId, uint256 fromAmount, uint256 toAmount)', 'params': [{'name': 'transactionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': '+EkepVBlqwGlrsSCQaLosmzg46eDGoHMwivdOeFF3Zc='}, {'name': 'integrator', 'type': 'string', 'indexed': False, 'decoded': True, 'value': 'lifi-api'}, {'name': 'referrer', 'type': 'string', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'receiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x0a099f602094575b5b3f1df876df379466d5a055'}, {'name': 'fromAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'toAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48'}, {'name': 'fromAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3838500714419267'}, {'name': 'toAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9777198'}]}}]"
