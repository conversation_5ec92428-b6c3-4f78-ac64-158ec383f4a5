block_height,block_signed_at,block_hash,tx_hash,tx_offset,miner_address,from_address,to_address,value,gas_offered,gas_spent,gas_price,fees_paid,successful,chain_id,chain_name,explorers,from_address_label,to_address_label,gas_metadata,gas_quote_rate,gas_quote,pretty_gas_quote,value_quote,pretty_value_quote,log_events
72970043,2025-06-19 18:12:18+00:00,0xb6112996d37ee83026c772102e013c5a781539c55f0e8c69267868fe81e18098,0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685,30,0x0000000000000000000000000000000000000000,0x8e76aebb0ead4362f7cec6f0fcb6b01b9577b64d,0xeeeeee9ec4769a09a76a83c7bc42b185872860ee,900000000000000000,305041,197216,34329358921,6770298848963936,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.18764892994450558,0.0012704393344126004,$0.00,0.16888403695005502,$0.17,"[{'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 110, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000008e76aebb0ead4362f7cec6f0fcb6b01b9577b64d', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da00000000000000000000000000000000000000000000000000000cb0aba61f929e4c000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000333a6ad5b89e4c0000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8e76aebb0ead4362f7cec6f0fcb6b01b9577b64d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '914419454338833996'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14419454338833996'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 111, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b75'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da00000000000000000000000000000000000000000000000000000c7d713b49da0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xac4c6e212a361c968f1725b4d055b47e63f80b75'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 112, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b75', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da00000000000000000000000000000000000000000000000000000c7d713b49da0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xac4c6e212a361c968f1725b4d055b47e63f80b75'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xad27827c312cd5e71311d68e180a9872d42de23d'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 113, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da00000000000000000000000000000000000000000000000000000c7d713b49da0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xad27827c312cd5e71311d68e180a9872d42de23d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 114, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da00000000000000000000000000000000000000000000000000000c7d713b49da0000000000000000000000000000000000000000000000e7f4bd878b5bbc817101b10000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e7f4bd9408ccf7cb4b01b1', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280417617954430545961222577'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280417618854430545961222577'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 115, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 116, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000504e73e75e5e88500f632ffe130fc91fce1559da', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000296cc', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x504e73e75e5e88500f632ffe130fc91fce1559da'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '169676'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 117, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000504e73e75e5e88500f632ffe130fc91fce1559da'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da0000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x504e73e75e5e88500f632ffe130fc91fce1559da'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 118, 'sender_address': '0x504e73e75e5e88500f632ffe130fc91fce1559da', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x504e73e75e5e88500f632ffe130fc91fce1559da.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000c7d713b49da0000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd6934000000000000000000000000000000000000000000000748e9c6ecc88a0fd3ef00000000000000000000000000000000000000000000000000172b9bb4141b56fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb876e', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '900000000000000000'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-169676'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '34401576365196884038639'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '6521872205486934'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-293010'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 119, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000296cc', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '169676'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 120, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xbbb02a24579dc2e59c1609253b6ddab5457ba00895b3eda80dd41e03e2cd7e55', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '0x0000000000000000000000000000000000000000000000000000000000000003'], 'raw_log_data': '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee0000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c33590000000000000000000000000000000000000000000000000c7d713b49da000000000000000000000000000000000000000000000000000000000000000296ccffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe4'}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 121, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000f70da97812cb96acdf810712aa562db8dfa3dbef'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000296cc', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf70da97812cb96acdf810712aa562db8dfa3dbef'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '169676'}]}}, {'block_height': 72970043, 'block_signed_at': '2025-06-19T18:12:18Z', 'tx_offset': 30, 'tx_hash': '0x9d597642b9747ca209a35adbe849734ac90128c60d18fe78c0e32136cfc0f685', 'log_offset': 122, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000008e76aebb0ead4362f7cec6f0fcb6b01b9577b64d', '0x000000000000000000000000eedba2484aaf940f37cd3cd21a5d7c4a7dafbfc0'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000180d8d09a234600000000000000000000000000000000000000000000000000cd5dfc07cb4f8a5000000000000000000000000000000000000000000021a3a58339e46b5a6a1b60000000000000000000000000000000000000000000000000cbdd2337312c445000000000000000000000000000000000000000000021a3a584babd3bf48d616', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8e76aebb0ead4362f7cec6f0fcb6b01b9577b64d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeedba2484aaf940f37cd3cd21a5d7c4a7dafbfc0'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '6770298844230752'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '924891316313454757'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2541709434537669961359798'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '918121017469224005'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2541709441307968805590550'}]}}]"
72970028,2025-06-19 18:11:46+00:00,0xdf8982a83d4a3df784dabf0712dc0eb40113c05fa8b011d6f24757dd769a937e,0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151,46,0x0000000000000000000000000000000000000000,0x3839e2c3a39280182975abf6ad556041119c950b,0xeeeeee9ec4769a09a76a83c7bc42b185872860ee,4660050000000000000,605063,350328,27500000046,9634020016115088,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.18764892994450558,0.0018078135470879447,$0.00,0.8744533959878932,$0.87,"[{'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 167, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000003839e2c3a39280182975abf6ad556041119c950b', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000041a798a903e639ad000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000fbc5e00e5719ad00000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3839e2c3a39280182975abf6ad556041119c950b'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4730917785258310061'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '70867785258310061'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 168, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b75'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000040abd2c8f58f20000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xac4c6e212a361c968f1725b4d055b47e63f80b75'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 169, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b75', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000040abd2c8f58f20000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xac4c6e212a361c968f1725b4d055b47e63f80b75'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xad27827c312cd5e71311d68e180a9872d42de23d'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 170, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000040abd2c8f58f20000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xad27827c312cd5e71311d68e180a9872d42de23d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 171, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000040abd2c8f58f2000000000000000000000000000000000000000000000e7f5591dc13e08d2a2105a0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e7f5595e6d10d1c831305a', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280420488023568067804139610'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280420492683618067804139610'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 172, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 173, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f1a12338d39fc085d8631e1a745b5116bc9b2a32', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000013cf9e254f701', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '348518918452993'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 174, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000f1a12338d39fc085d8631e1a745b5116bc9b2a32'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f2000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 175, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000040abd2c8f58f2000fffffffffffffffffffffffffffffffffffffffffffffffffffec3061dab08ff00000000000000000000000000000000000000000236deaf38d3e4418a47c1880000000000000000000000000000000000000000000000149272d19595614f69fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe8cde', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '4660050000000000000'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-348518918452993'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '685303611536157529646547336'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '379487608791460040553'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-95010'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 176, 'sender_address': '0x53e0bca35ec356bd5dddfebbd1fc0fd03fabad39', 'sender_address_label': 'ChainLink Token (LINK)', 'sender_factory_address': None, 'sender_name': 'ChainLink Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'LINK', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x53e0bca35ec356bd5dddfebbd1fc0fd03fabad39.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003dc10d7bfb94eeb009203e84a653e5764f71771d', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000eccfe5d722c597', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '66656680567555479'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 177, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003dc10d7bfb94eeb009203e84a653e5764f71771d'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000013cf9e254f701', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '348518918452993'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 178, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffff13301a28dd3a6900000000000000000000000000000000000000000000000000013cf9e254f70100000000000000000000000000000000000000001281a773ddb70461307b71f50000000000000000000000000000000000000000000000530d50e4ebf9ce32b6ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff32c0', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-66656680567555479'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '348518918452993'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '5727472380068949126524137973'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '1532039276540682187446'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-52544'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 179, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000022177148e681a6ca5242c9888ace170ee7ec47bd', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000d5599', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x22177148e681a6ca5242c9888ace170ee7ec47bd'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '873881'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 180, 'sender_address': '0x53e0bca35ec356bd5dddfebbd1fc0fd03fabad39', 'sender_address_label': 'ChainLink Token (LINK)', 'sender_factory_address': None, 'sender_name': 'ChainLink Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'LINK', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x53e0bca35ec356bd5dddfebbd1fc0fd03fabad39.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x00000000000000000000000022177148e681a6ca5242c9888ace170ee7ec47bd'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000eccfe5d722c597', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x22177148e681a6ca5242c9888ace170ee7ec47bd'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '66656680567555479'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 181, 'sender_address': '0x22177148e681a6ca5242c9888ace170ee7ec47bd', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x22177148e681a6ca5242c9888ace170ee7ec47bd.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffff2aa6700000000000000000000000000000000000000000000000000eccfe5d722c59700000000000000000000000000000000000436e04451f0f4fa906c976317f3ce00000000000000000000000000000000000000000000000000017eb90c2c1ac3000000000000000000000000000000000000000000000000000000000003d2dd', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-873881'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '66656680567555479'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '21882205805181242185378730736088014'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '420808214977219'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '250589'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 182, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000d5599', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '873881'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 183, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xbbb02a24579dc2e59c1609253b6ddab5457ba00895b3eda80dd41e03e2cd7e55', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '0x0000000000000000000000000000000000000000000000000000000000000003'], 'raw_log_data': '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa8417400000000000000000000000000000000000000000000000040abd2c8f58f200000000000000000000000000000000000000000000000000000000000000d5599ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeef9'}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 184, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x0000000000000000000000003839e2c3a39280182975abf6ad556041119c950b'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000d5599', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3839e2c3a39280182975abf6ad556041119c950b'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '873881'}]}}, {'block_height': 72970028, 'block_signed_at': '2025-06-19T18:11:46Z', 'tx_offset': 46, 'tx_hash': '0xa134e7c77893e2d9f6aafb3598b95025b9ef09e80b81156318ca45ef4357d151', 'log_offset': 185, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000003839e2c3a39280182975abf6ad556041119c950b', '0x0000000000000000000000009ead03f7136fc6b4bdb0780b00a1c14ae5a8b6d0'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000223a171451825000000000000000000000000000000000000000000000000041e2b5f43c0e50ef000000000000000000000000000000000000000000000a763d85a565385c0bc100000000000000000000000000000000000000000000000041c07bdd27bcce9f000000000000000000000000000000000000000000000a763da7df7c4cad8e11', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3839e2c3a39280182975abf6ad556041119c950b'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9ead03f7136fc6b4bdb0780b00a1c14ae5a8b6d0'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9634020007707216'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4747557017786142959'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '49404813760656551185345'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4737922997778435743'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '49404823394676558892561'}]}}]"
72970021,2025-06-19 18:11:32+00:00,0x88e2a028ae0740dd638c5894034184195a82ca3eb2d539059c47feff0a371fa3,0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7,165,0x0000000000000000000000000000000000000000,0xcd769a5b5fd761c60265c8ab0eee38a37accb8fd,0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae,65400000000000000000,606406,466466,30000000000,13993980000000000,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.18764892994450558,0.002625955372664812,$0.00,12.272240018370665,$12.27,"[{'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 437, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000cd769a5b5fd761c60265c8ab0eee38a37accb8fd', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000038b9b797ef68c00000000000000000000000000000000000000000000000000039a7c2ee37f0ee08f00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ee0b5648882e08f0000000000000000000000000000000000000000000000038b9b797ef68c0000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd769a5b5fd761c60265c8ab0eee38a37accb8fd'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '65400000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '66472056154705813647'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1072056154705813647'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '65400000000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 438, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x000000000000000000000000bd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000038b9b797ef68c00000000000000000000000000000000000000000000000000038b9b797ef68c0000000000000000000000000000000000000000000000002588933758661979b622000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000258c1ed2d1e51005b622', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '65400000000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '65400000000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '177246925104844203734562'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '177312325104844203734562'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 439, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000bd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e8400000000000000000000000000000000000000000000000258c1ed2d1e51005b62200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000025889aee4c80e81d762200000000000000000000000000000000000000000000000383e4856427e84000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '177312325104844203734562'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '177247481004844203734562'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 440, 'sender_address': '0xbd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xbd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9.png', 'raw_log_topics': ['0x28a87b6059180e46de5fb9ab35eb043e8fe00ab45afcc7789e3934ecbbcde3ea', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x0000000000000000000000001bcc58d165e5374d7b492b21c0a572fd61c0c2a0'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000075434b3111b90000000000000000000000000000000000000000000000000000062bf67bd883000', 'decoded': {'name': 'FeesCollected', 'signature': 'FeesCollected(indexed address _token, indexed address _integrator, uint256 _integratorFee, uint256 _lifiFee)', 'params': [{'name': '_token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': '_integrator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1bcc58d165e5374d7b492b21c0a572fd61c0c2a0'}, {'name': '_integratorFee', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '528105000000000000'}, {'name': '_lifiFee', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '27795000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 441, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0x3221ef7c18dfea4978b12475a576cc6ffc0dc71d30847493d3bacb694441ddef000000000000000000000000bd6c7b0d2f68c2b7805d88388319cfb6ecb50ea9000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000038b9b797ef68c000000000000000000000000000000000000000000000000000383e4856427e8400000000000000000000000000000000000000000000000000000000000685452d4'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 442, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e8400000000000000000000000000000000000000000000000000383e4856427e840000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000383e4856427e84000', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 443, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc', '0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e8400000000000000000000000000000000000000000000000000383e4856427e84000000000000000000000000000000000000000000000e7f56b0793e2c9297ec88f0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000e7f56e8b78682d5167088f', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280420818466927622810945679'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '280420883311027622810945679'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 444, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e84000', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 445, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000b6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ba32c4', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xb6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '12202692'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 446, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc', '0x000000000000000000000000b6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e84000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xb6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 447, 'sender_address': '0xb6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xb6e57ed85c4c9dbfef2a68711e9d6f36c56e0fcb.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000383e4856427e84000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff45cd3c000000000000000000000000000000000000000000000747a0314ee6aecee5f6000000000000000000000000000000000000000000000000240352dde4ac0f55fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8760', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-12202692'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '34377827303393504191990'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '2595008923273269077'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-293024'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 448, 'sender_address': '0x6140b987d6b51fd75b66c3b07733beb5167c42fc', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x6140b987d6b51fd75b66c3b07733beb5167c42fc.png', 'raw_log_topics': ['0x2db5ddd0b42bdbca0d69ea16f234a870a485854ae0d91f16643d6f317d8b8994', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '0x0000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c3359'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae00000000000000000000000000000000000000000000000383e4856427e840000000000000000000000000000000000000000000000000000000000000b0e1730000000000000000000000000000000000000000000000000000000000ba32c4', 'decoded': {'name': 'Route', 'signature': 'Route(indexed address from, address to, indexed address tokenIn, indexed address tokenOut, uint256 amountIn, uint256 amountOutMin, uint256 amountOut)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'to', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'tokenIn', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'}, {'name': 'tokenOut', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359'}, {'name': 'amountIn', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '64844100000000000000'}, {'name': 'amountOutMin', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11592051'}, {'name': 'amountOut', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '12202692'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 449, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0x3221ef7c18dfea4978b12475a576cc6ffc0dc71d30847493d3bacb694441ddef0000000000000000000000006140b987d6b51fd75b66c3b07733beb5167c42fc00000000000000000000000000000000000000000000000000000000000000000000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c335900000000000000000000000000000000000000000000000383e4856427e840000000000000000000000000000000000000000000000000000000000000ba32c400000000000000000000000000000000000000000000000000000000685452d4'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 450, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x000000000000000000000000337685fdab40d39bd02028545a4ffa7d287cc3e2'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ba32c4', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x337685fdab40d39bd02028545a4ffa7d287cc3e2'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '12202692'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 451, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000337685fdab40d39bd02028545a4ffa7d287cc3e2', '0x000000000000000000000000c38e4e6a15593f908255214653d3d947ca1c2338'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ba32c4', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x337685fdab40d39bd02028545a4ffa7d287cc3e2'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xc38e4e6a15593f908255214653d3d947ca1c2338'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '12202692'}]}}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 452, 'sender_address': '0xc38e4e6a15593f908255214653d3d947ca1c2338', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xc38e4e6a15593f908255214653d3d947ca1c2338.png', 'raw_log_topics': ['0x918554b6bd6e2895ce6553de5de0e1a69db5289aa0e4fe193a0dcd1f14347477'], 'raw_log_data': '0xb8a7a89002f7f9b4ad3fcc1bc8e62da9b1b6b67794c2c2d7b656ff173fb8ccfc'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 453, 'sender_address': '0x337685fdab40d39bd02028545a4ffa7d287cc3e2', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x337685fdab40d39bd02028545a4ffa7d287cc3e2.png', 'raw_log_topics': ['0xbf150db6b4a14b084f7346b4bc300f552ce867afe55be27bce2d6b37e3307cda'], 'raw_log_data': '0x0000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c33590000000000000000000000000000000000000000000000000000000000ba32c4000000000000000000000000c38e4e6a15593f908255214653d3d947ca1c2338000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000001e48e8d142b0000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c33590000000000000000000000000000000000000000000000000000000000ba32c4000000000000000000000000cd769a5b5fd761c60265c8ab0eee38a37accb8fd000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000006e86820000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000008c7e4000000000000000000000000000000000000000000000000000000000000050f0000000000000000000000000000000000000000000000000000000068545ee1f2c733c0825c6e9fa8f04689386afb913aefb447a5aab460e3830dcf9addc8070000000000000000000000000000000000000000000000000000000000000001f31e1b125dc6b5bdd16c2ee0e6e18df1858279ea5fd8e8f3a5584adb9c19d1c8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000024328143d25b6d3321d32c4edf83e6a7971c9dd15572cb855c58ceee6ba9dc99300000000000000000000000000000000000000000000000000000000'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 454, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0x7be3e48a8a8b4d32138937e1809ac83481fffe48e49bb60e43ed1d3d50349e4c', '0x3221ef7c18dfea4978b12475a576cc6ffc0dc71d30847493d3bacb694441ddef', '0x000000000000000000000000000000000000000000000000000416edef1601be'], 'raw_log_data': '0xf2c733c0825c6e9fa8f04689386afb913aefb447a5aab460e3830dcf9addc807'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 455, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0xcba69f43792f9f399347222505213b55af8e0b0b54b893085c2e27ecbe1644f1'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000000203221ef7c18dfea4978b12475a576cc6ffc0dc71d30847493d3bacb694441ddef0000000000000000000000000000000000000000000000000000000000000140000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003c499c542cef5e3811e1192ce70d8cc03d5c335900000000000000000000000011f111f111f111f111f111f111f111f111f111f10000000000000000000000000000000000000000000000000000000000ba32c4000000000000000000000000000000000000000000000000000416edef1601be0000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000056d6179616e00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000077068616e746f6d00000000000000000000000000000000000000000000000000'}, {'block_height': 72970021, 'block_signed_at': '2025-06-19T18:11:32Z', 'tx_offset': 165, 'tx_hash': '0x165c9825dea1d8988f6176b0694570bed5b80c9799dafd9680c87c832e85d5d7', 'log_offset': 456, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000cd769a5b5fd761c60265c8ab0eee38a37accb8fd', '0x0000000000000000000000009ead03f7136fc6b4bdb0780b00a1c14ae5a8b6d0'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000031b7735e4841140000000000000000000000000000000000000000000000039abcd09479a5e88f000000000000000000000000000000000000000000000a756b57cecca6f03d660000000000000000000000000000000000000000000000039a8b19211b5da77b000000000000000000000000000000000000000000000a756b89864005387e7a', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd769a5b5fd761c60265c8ab0eee38a37accb8fd'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9ead03f7136fc6b4bdb0780b00a1c14ae5a8b6d0'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '13993979989737748'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '66490248334705813647'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '49389668763583871204710'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '66476254354716075899'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '49389682757563860942458'}]}}]"
