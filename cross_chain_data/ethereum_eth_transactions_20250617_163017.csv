block_height,block_signed_at,block_hash,tx_hash,tx_offset,miner_address,from_address,to_address,value,gas_offered,gas_spent,gas_price,fees_paid,successful,chain_id,chain_name,explorers,from_address_label,to_address_label,gas_metadata,gas_quote_rate,gas_quote,pretty_gas_quote,value_quote,pretty_value_quote,log_events
22723783,2025-06-17 10:59:11+00:00,0xc9ac7845bb94484a3ab497e6398c74e57397f0f40976a574c5b5382e3b358eb9,0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0,151,******************************************,******************************************,******************************************,4081957480434608,402570,284790,1241426499,353545852650210,True,1,eth-mainnet,"[{'label': 'Etherscan', 'url': 'https://etherscan.io/tx/0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1669653891-eth.svg'}",2554.5847964499326,0.9031628600281546,$0.90,10.427706519273322,$10.43,"[{'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 388, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************00000000000e8084c99c0bb0', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4081957480434608'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 389, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x000000000000000000000000fff8d5fff6ee3226fa2f5d7d5d8c3ff785be9c74'], 'raw_log_data': '******************************************00000000000e8084c99c0bb0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4081957480434608'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 390, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Kekius Maximus', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'KEKIUS', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000fff8d5fff6ee3226fa2f5d7d5d8c3ff785be9c74', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************000000000000004c3b818b88', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '327415860104'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 391, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': '0x5c69bee701ef814a2b6a3edd4b1652cb9cc5aa6f', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '******************************************00000000006c3d3610a363b0000000000000000000000000000000000000000000000014877c444cbbdb06cb', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '30466599901422512'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '378697634662783321803'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 392, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': '0x5c69bee701ef814a2b6a3edd4b1652cb9cc5aa6f', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c'], 'raw_log_data': '******************************************000000000000000000000000000000000000000000000000000000000000000000000000000e8084c99c0bb00000000000000000000000000000000000000000000000000000004c3b818b880000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4081957480434608'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '327415860104'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 393, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Kekius Maximus', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'KEKIUS', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003ced11c610556e5292fbc2e75d68c3899098c14c', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '******************************************000000000000004c3b818b88', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '327415860104'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 394, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xbbb02a24579dc2e59c1609253b6ddab5457ba00895b3eda80dd41e03e2cd7e55', '0x000000000000000000000000ad27827c312cd5e71311d68e180a9872d42de23d', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '******************************************000000000000000000000000'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae00000000000000000000000026e550ac11b26f78a04489d5f20f24e3559f7dd9000000000000000000000000000000000000000000000000000e8084c99c0bb00000000000000000000000000000000000000000000000000000004c3b818b88ffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbb49f80'}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 395, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Kekius Maximus', 'sender_contract_decimals': 9, 'sender_contract_ticker_symbol': 'KEKIUS', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000003b479b856597e8f7c41a8f3be566839cfd810656'], 'raw_log_data': '******************************************000000000000004c3b818b88', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '327415860104'}]}}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 396, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0xf81bf78e9272b48dc5fa90b9d39799602621b4eae3c9f3e0f5a6c4bcc6b9f7d1000000000000000000000000ac4c6e212a361c968f1725b4d055b47e63f80b75000000000000000000000000000000000000000000000000000000000000000000000000000000000000000026e550ac11b26f78a04489d5f20f24e3559f7dd9000000000000000000000000000000000000000000000000000e8084c99c0bb00000000000000000000000000000000000000000000000000000004c3b818b880000000000000000000000000000000000000000000000000000000068514a7f'}, {'block_height': 22723783, 'block_signed_at': '2025-06-17T10:59:11Z', 'tx_offset': 151, 'tx_hash': '0x5eeca8ee21a1a2eee36c3e80c9521501adeacbbf20f5ddbf6f736d20ef1448e0', 'log_offset': 397, 'sender_address': '0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0x1231deb6f5749ef6ce6943a275a1d3e7486f4eae.png', 'raw_log_topics': ['0x38eee76fd911eabac79da7af16053e809be0e12c8637f156e77e1af309b99537', '0xf81bf78e9272b48dc5fa90b9d39799602621b4eae3c9f3e0f5a6c4bcc6b9f7d1'], 'raw_log_data': '******************************************0000000000000000000000e000000000000000000000000000000000000000000000000000000000000001200000000000000000000000003b479b856597e8f7c41a8f3be566839cfd810656000000000000000000000000000000000000000000000000000000000000000000000000000000000000000026e550ac11b26f78a04489d5f20f24e3559f7dd9000000000000000000000000000000000000000000000000000e8084c99c0bb00000000000000000000000000000000000000000000000000000004c3b818b88000000000000000000000000000000000000000000000000000000000000000d62696e616e636557616c6c657400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a30783030303030303030303030303030303030303030303030303030303030303030303030303030303000000000000000000000000000000000000000000000', 'decoded': {'name': 'LiFiGenericSwapCompleted', 'signature': 'LiFiGenericSwapCompleted(indexed bytes32 transactionId, string integrator, string referrer, address receiver, address fromAssetId, address toAssetId, uint256 fromAmount, uint256 toAmount)', 'params': [{'name': 'transactionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': '+Bv3jpJytI3F+pC505eZYCYhtOrjyfPg9abEvMa599E='}, {'name': 'integrator', 'type': 'string', 'indexed': False, 'decoded': True, 'value': 'binanceWallet'}, {'name': 'referrer', 'type': 'string', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'receiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'toAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4081957480434608'}, {'name': 'toAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '327415860104'}]}}]"
22723782,2025-06-17 10:58:59+00:00,0xe3cb5e9dcc6dff621c665b48b7ec53fbc47b2bdd2fe9a126bb86fbc153d295ea,0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8,0,******************************************,******************************************,******************************************,115,836837,585786,19347970281,11333770119025866,True,1,eth-mainnet,"[{'label': 'Etherscan', 'url': 'https://etherscan.io/tx/0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1669653891-eth.svg'}",2554.5847964499326,28.95307683252202,$28.95,2.9377725159174224e-13,$0.00,"[{'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 0, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3'], 'raw_log_data': '******************************************000000000000000d2ae19d00', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '56554003712'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 1, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f', '0x21c67e77068de97969ba93d4aab21826d33ca12bb9f565d8496e8fda8a82ca27', '0x00000000000000000000000066a9893cc07d91d95644aedd05d03f95e1dba8af'], 'raw_log_data': '******************************************0000000132ed558c629021aefffffffffffffffffffffffffffffffffffffffffffffffffffffff2d51e630000000000000000000000000000000000000000000003503b00946222a77a2da600000000000000000000000000000000000000000000000076b2416027c9a23efffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcfb1e00000000000000000000000000000000000000000000000000000000000001f4'}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 2, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90'], 'raw_log_data': '******************************************000000000000000d2ae19d00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '56554003712'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 3, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************0000000132ed53f6088e6a0c', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '22116425661256985100'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 4, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000088e6a0c2ddd26feeb64f039a2c41296fcb3f5640', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************00000000c6bfc46200000001', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14321381265247305729'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 5, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x00000000000000000000000088e6a0c2ddd26feeb64f039a2c41296fcb3f5640'], 'raw_log_data': '******************************************000000000000000886d2f2e0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '36621710048'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 6, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************000000000000000886d2f2e0ffffffffffffffffffffffffffffffffffffffffffffffff39403b9dffffffff0000000000000000000000000000000000004d42ce172b3693df38644ca41c1e00000000000000000000000000000000000000000000000044d728d74d92e8ff00000000000000000000000000000000000000000000000000000000000304e1', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '36621710048'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-14321381265247305729'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '1567038380128505655386362448452638'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '4960478419756443903'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '197857'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 7, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000836951eb21f3df98273517b7249dceff270d34bf'], 'raw_log_data': '******************************************00000000000000086c25ab00', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x836951eb21f3df98273517b7249dceff270d34bf'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '36174146304'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 8, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x00000000000000000000000052aa899454998be5b000ad077a46bbe360f4e497'], 'raw_log_data': '******************************************00000000000000086c25ab00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x52aa899454998be5b000ad077a46bbe360f4e497'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '36174146304'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 9, 'sender_address': '0x52aa899454998be5b000ad077a46bbe360f4e497', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0x52aa899454998be5b000ad077a46bbe360f4e497.png', 'raw_log_topics': ['0x4d93b232a24e82b284ced7461bf4deacffe66759d5c24513e6f29e571ad78d15', '0x000000000000000000000000836951eb21f3df98273517b7249dceff270d34bf', '0x000000000000000000000000a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48'], 'raw_log_data': '******************************************00000000000000052b4b3340fffffffffffffffffffffffffffffffffffffffffffffffffffffffcbf258841000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000009c8d27ad895500000000000000000000d2f8182f01360000000000000000084985659f1000000805c3ac2959a145260c01e77d43e8021d'}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 10, 'sender_address': '0x52aa899454998be5b000ad077a46bbe360f4e497', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0x52aa899454998be5b000ad077a46bbe360f4e497.png', 'raw_log_topics': ['0x4d93b232a24e82b284ced7461bf4deacffe66759d5c24513e6f29e571ad78d15', '0x000000000000000000000000836951eb21f3df98273517b7249dceff270d34bf', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffff8783666e0a983a800000000000000000000000000000000000000000000000004bd4f214a4f45d00000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e300000000000000009177c1c8731706150000000000000000b0d911510d9b08150000000000000007caac4bdef8000007abbe460911a144e61c01e82c43e80124'}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 11, 'sender_address': '0x836951eb21f3df98273517b7249dceff270d34bf', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/0x836951eb21f3df98273517b7249dceff270d34bf.png', 'raw_log_topics': ['0xdc004dbca4ef9c966218431ee5d9133d337ad018dd5b5c5493722803f75c64f7'], 'raw_log_data': '******************************************000000000000000000000001000000000000000000000000000000000000000000000000000000086c25ab00000000000000000000000000000000000000000000000000c4518ba69a5c2280000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', 'decoded': {'name': 'Swap', 'signature': 'Swap(bool swap0to1, uint256 amountIn, uint256 amountOut, address to)', 'params': [{'name': 'swap0to1', 'type': 'bool', 'indexed': False, 'decoded': True, 'value': True}, {'name': 'amountIn', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '36174146304'}, {'name': 'amountOut', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14146241452218000000'}, {'name': 'to', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 12, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************00000000c4518b75d0dac17f', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14146241242678870399'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 13, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000000000000022d473030f116ddee9f6b43ac78ba3'], 'raw_log_data': '******************************************00000000000000044d2a3e00', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '18474483200'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 14, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0x40e9cecb9f5f1f1c5b9c97dec2917b7ee92e57ba5563708daca94dd84ad7112f', '0x72331fcb696b0151904c03584b66dc8365bc63f8a144d89a773384e3a579ca73', '0x00000000000000000000000066a9893cc07d91d95644aedd05d03f95e1dba8af'], 'raw_log_data': '******************************************000000006449ea8df7bcb775fffffffffffffffffffffffffffffffffffffffffffffffffffffffbb2d5c200000000000000000000000000000000000000000000035026688488e881c22d6f0000000000000000000000000000000000000000000000001ab8fef09125c60efffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcfb1c00000000000000000000000000000000000000000000000000000000000001f4'}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 15, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000000000000004444c5dc75cb358380d2e3de08a90'], 'raw_log_data': '******************************************00000000000000044d2a3e00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '18474483200'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 16, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************000000006449ea5d66c7bbec', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '7226564763969895404'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 17, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '******************************************000000003838e4ee2133c111', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4051239576230150417'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 18, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000c7bbec68d12a0d1830360f8ec58fa599ba1b0e9b'], 'raw_log_data': '******************************************000000000000000269453700', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10356078336'}]}}, {'block_height': 22723782, 'block_signed_at': '2025-06-17T10:58:59Z', 'tx_offset': 0, 'tx_hash': '0xb9ca494265ac2ba28c9d7702d2f3469ade0cd3adf44be23c6c494db9962139c8', 'log_offset': 19, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/1/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3', '0x000000000000000000000000d4bc53434c5e12cb41381a556c3c47e1a86e80e3'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffc7c71b11decc3eef0000000000000000000000000000000000000000000000000000000269453700000000000000000000000000000000000000000000035054149c5400859ed1da00000000000000000000000000000000000000000000000009ee8bdeceb39a29fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcfb20', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-4051239576230150417'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '10356078336'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '4006117789129989087482330'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '715663179865365033'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-197856'}]}}]"
