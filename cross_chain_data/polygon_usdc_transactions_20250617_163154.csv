block_height,block_signed_at,block_hash,tx_hash,tx_offset,miner_address,from_address,to_address,value,gas_offered,gas_spent,gas_price,fees_paid,successful,chain_id,chain_name,explorers,from_address_label,to_address_label,gas_metadata,gas_quote_rate,gas_quote,pretty_gas_quote,value_quote,pretty_value_quote,log_events
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594,79,0x0000000000000000000000000000000000000000,0x125914b1d921d83367a0478f4ea6447aff2ee9da,0x56c79347e95530c01a2fc76e732f9566da16e113,0,1800000,794701,26000000055,20662226043708555,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.00409746101953221,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 644, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000002245cdbf', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4f46886de33690f98ca4e569d57704b476ee9136'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '574999999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 645, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffa91f3bf5b6', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4f46886de33690f98ca4e569d57704b476ee9136'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007539991508406'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 646, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c0bc7ae164a8d8e074359424d2c4d35654d1a43a', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000044aa20', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xc0bc7ae164a8d8e074359424d2c4d35654d1a43a'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4500000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 647, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000c0bc7ae164a8d8e074359424d2c4d35654d1a43a', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffe00085d65', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xc0bc7ae164a8d8e074359424d2c4d35654d1a43a'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007904540253541'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 648, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000989680', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 649, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffff6df95ab71f228', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457581438997282026024'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 650, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000002c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b8000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000009896800000000000000000000000000000000000000000000000000000000000989680', 'decoded': {'name': 'TransferBatch', 'signature': 'TransferBatch(indexed address _operator, indexed address _from, indexed address _to, uint256[] _ids, uint256[] _amounts)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_ids', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '82778075240613754181663052787751937303431519174245552377929584495420563833016', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': '_amounts', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '10000000', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '10000000', 'bitSize': 256, 'typeAsString': 'uint256'}]}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 651, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x2e6bb91f8cbcda0c93623c54d0403a43514fabc40084ec96b6d5379a74786298', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x914014d4390f1f00d5619023a06ca1f4385e0f7a8fd50cba6f0fe230357d89ad'], 'raw_log_data': '0x0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa8417400000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000989680000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002', 'decoded': {'name': 'PositionSplit', 'signature': 'PositionSplit(indexed address stakeholder, address collateralToken, indexed bytes32 parentCollectionId, indexed bytes32 conditionId, uint256[] partition, uint256 amount)', 'params': [{'name': 'stakeholder', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'collateralToken', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174'}, {'name': 'parentCollectionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='}, {'name': 'conditionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'kUAU1DkPHwDVYZAjoGyh9DheD3qP1Qy6bw/iMDV9ia0='}, {'name': 'partition', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '1', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '2', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 652, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x000000000000000000000000c0bc7ae164a8d8e074359424d2c4d35654d1a43a'], 'raw_log_data': '0xc9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd2950000000000000000000000000000000000000000000000000000000000989680', 'decoded': {'name': 'TransferSingle', 'signature': 'TransferSingle(indexed address _operator, indexed address _from, indexed address _to, uint256 _id, uint256 _amount)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xc0bc7ae164a8d8e074359424d2c4d35654d1a43a'}, {'name': '_id', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317'}, {'name': '_amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 653, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0xd0a08e8c493f9c94f29311604c9de1b4e8c8d4c06bd0c789af57f2d65bfec0f6', '0x617ff38bf65483ac639e63b01660bcc145152c45a02d5173f52d18cbf1c0e290', '0x000000000000000000000000c0bc7ae164a8d8e074359424d2c4d35654d1a43a', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295000000000000000000000000000000000000000000000000000000000044aa2000000000000000000000000000000000000000000000000000000000009896800000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 654, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000445e4827f2edf68534defd311fcebd55657a4426', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000225510', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x445e4827f2edf68534defd311fcebd55657a4426'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2250000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 655, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000445e4827f2edf68534defd311fcebd55657a4426', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffff4af12a9', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x445e4827f2edf68534defd311fcebd55657a4426'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007912939786921'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 656, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000004c4b40', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 657, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffff6df95ab25a6e8', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457581438997277026024'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 658, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000002c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b8000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000004c4b4000000000000000000000000000000000000000000000000000000000004c4b40', 'decoded': {'name': 'TransferBatch', 'signature': 'TransferBatch(indexed address _operator, indexed address _from, indexed address _to, uint256[] _ids, uint256[] _amounts)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_ids', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '82778075240613754181663052787751937303431519174245552377929584495420563833016', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': '_amounts', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '5000000', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '5000000', 'bitSize': 256, 'typeAsString': 'uint256'}]}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 659, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x2e6bb91f8cbcda0c93623c54d0403a43514fabc40084ec96b6d5379a74786298', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x914014d4390f1f00d5619023a06ca1f4385e0f7a8fd50cba6f0fe230357d89ad'], 'raw_log_data': '0x0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa84174000000000000000000000000000000000000000000000000000000000000006000000000000000000000000000000000000000000000000000000000004c4b40000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002', 'decoded': {'name': 'PositionSplit', 'signature': 'PositionSplit(indexed address stakeholder, address collateralToken, indexed bytes32 parentCollectionId, indexed bytes32 conditionId, uint256[] partition, uint256 amount)', 'params': [{'name': 'stakeholder', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'collateralToken', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174'}, {'name': 'parentCollectionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='}, {'name': 'conditionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'kUAU1DkPHwDVYZAjoGyh9DheD3qP1Qy6bw/iMDV9ia0='}, {'name': 'partition', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '1', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '2', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 660, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x000000000000000000000000445e4827f2edf68534defd311fcebd55657a4426'], 'raw_log_data': '0xc9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd29500000000000000000000000000000000000000000000000000000000004c4b40', 'decoded': {'name': 'TransferSingle', 'signature': 'TransferSingle(indexed address _operator, indexed address _from, indexed address _to, uint256 _id, uint256 _amount)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x445e4827f2edf68534defd311fcebd55657a4426'}, {'name': '_id', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317'}, {'name': '_amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 661, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0xd0a08e8c493f9c94f29311604c9de1b4e8c8d4c06bd0c789af57f2d65bfec0f6', '0xaf37bf30392872cc1b4935569d40d83b5cd9108ca672721525bb8bff6bfe0085', '0x000000000000000000000000445e4827f2edf68534defd311fcebd55657a4426', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295000000000000000000000000000000000000000000000000000000000022551000000000000000000000000000000000000000000000000000000000004c4b400000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 662, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009304c4a06dbf0893ee8bc556697a92374a80f693', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000005aa3200', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9304c4a06dbf0893ee8bc556697a92374a80f693'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '95040000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 663, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000009304c4a06dbf0893ee8bc556697a92374a80f693', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffcbfd4d9164', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9304c4a06dbf0893ee8bc556697a92374a80f693'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007689746092388'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 664, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000cdfe600', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '216000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 665, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffff6df959e45c0e8', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457581438997061026024'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 666, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000002c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b80000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000cdfe600000000000000000000000000000000000000000000000000000000000cdfe600', 'decoded': {'name': 'TransferBatch', 'signature': 'TransferBatch(indexed address _operator, indexed address _from, indexed address _to, uint256[] _ids, uint256[] _amounts)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_ids', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '82778075240613754181663052787751937303431519174245552377929584495420563833016', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': '_amounts', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '216000000', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '216000000', 'bitSize': 256, 'typeAsString': 'uint256'}]}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 667, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x2e6bb91f8cbcda0c93623c54d0403a43514fabc40084ec96b6d5379a74786298', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x914014d4390f1f00d5619023a06ca1f4385e0f7a8fd50cba6f0fe230357d89ad'], 'raw_log_data': '0x0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa841740000000000000000000000000000000000000000000000000000000000000060000000000000000000000000000000000000000000000000000000000cdfe600000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002', 'decoded': {'name': 'PositionSplit', 'signature': 'PositionSplit(indexed address stakeholder, address collateralToken, indexed bytes32 parentCollectionId, indexed bytes32 conditionId, uint256[] partition, uint256 amount)', 'params': [{'name': 'stakeholder', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'collateralToken', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174'}, {'name': 'parentCollectionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='}, {'name': 'conditionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'kUAU1DkPHwDVYZAjoGyh9DheD3qP1Qy6bw/iMDV9ia0='}, {'name': 'partition', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '1', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '2', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '216000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 668, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000009304c4a06dbf0893ee8bc556697a92374a80f693'], 'raw_log_data': '0xc9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295000000000000000000000000000000000000000000000000000000000cdfe600', 'decoded': {'name': 'TransferSingle', 'signature': 'TransferSingle(indexed address _operator, indexed address _from, indexed address _to, uint256 _id, uint256 _amount)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9304c4a06dbf0893ee8bc556697a92374a80f693'}, {'name': '_id', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317'}, {'name': '_amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '216000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 669, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0xd0a08e8c493f9c94f29311604c9de1b4e8c8d4c06bd0c789af57f2d65bfec0f6', '0x0873c61660c200f5455bbbfe9d577a88cb170f1080b6b4247528a03cd5a7cfdc', '0x0000000000000000000000009304c4a06dbf0893ee8bc556697a92374a80f693', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd2950000000000000000000000000000000000000000000000000000000005aa3200000000000000000000000000000000000000000000000000000000000cdfe6000000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 670, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000079dbc666f24349bf8f2cc4ef10269b6f60a8fd14', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000140b7ef7', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x79dbc666f24349bf8f2cc4ef10269b6f60a8fd14'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '336297719'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 671, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000079dbc666f24349bf8f2cc4ef10269b6f60a8fd14', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffe4fb49dd776', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x79dbc666f24349bf8f2cc4ef10269b6f60a8fd14'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584006056439043958'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 672, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000002e9db626', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '782087718'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 673, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004d97dcd97ec945f40cf65f87097ace5ea0476045'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffff6df956fa80ac2', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457581438996278938306'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 674, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x4a39dc06d4c0dbc64b70af90fd698a233a518aa5d07e595d983b8c0526c8f7fb', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000a00000000000000000000000000000000000000000000000000000000000000002c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b80000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000002e9db626000000000000000000000000000000000000000000000000000000002e9db626', 'decoded': {'name': 'TransferBatch', 'signature': 'TransferBatch(indexed address _operator, indexed address _from, indexed address _to, uint256[] _ids, uint256[] _amounts)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_ids', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '82778075240613754181663052787751937303431519174245552377929584495420563833016', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': '_amounts', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '782087718', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '782087718', 'bitSize': 256, 'typeAsString': 'uint256'}]}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 675, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0x2e6bb91f8cbcda0c93623c54d0403a43514fabc40084ec96b6d5379a74786298', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000000000000000000000000000000000000000000000', '0x914014d4390f1f00d5619023a06ca1f4385e0f7a8fd50cba6f0fe230357d89ad'], 'raw_log_data': '0x0000000000000000000000002791bca1f2de4661ed88a30c99a7a9449aa841740000000000000000000000000000000000000000000000000000000000000060000000000000000000000000000000000000000000000000000000002e9db626000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002', 'decoded': {'name': 'PositionSplit', 'signature': 'PositionSplit(indexed address stakeholder, address collateralToken, indexed bytes32 parentCollectionId, indexed bytes32 conditionId, uint256[] partition, uint256 amount)', 'params': [{'name': 'stakeholder', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': 'collateralToken', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174'}, {'name': 'parentCollectionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='}, {'name': 'conditionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'kUAU1DkPHwDVYZAjoGyh9DheD3qP1Qy6bw/iMDV9ia0='}, {'name': 'partition', 'type': 'uint256[]', 'indexed': False, 'decoded': True, 'value': [{'value': '1', 'bitSize': 256, 'typeAsString': 'uint256'}, {'value': '2', 'bitSize': 256, 'typeAsString': 'uint256'}]}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '782087718'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 676, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x00000000000000000000000079dbc666f24349bf8f2cc4ef10269b6f60a8fd14'], 'raw_log_data': '0xc9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd295000000000000000000000000000000000000000000000000000000002e9db626', 'decoded': {'name': 'TransferSingle', 'signature': 'TransferSingle(indexed address _operator, indexed address _from, indexed address _to, uint256 _id, uint256 _amount)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x79dbc666f24349bf8f2cc4ef10269b6f60a8fd14'}, {'name': '_id', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '91277872763691191055389101221156464654455759174116552730546047765010697671317'}, {'name': '_amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '782087718'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 677, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0xd0a08e8c493f9c94f29311604c9de1b4e8c8d4c06bd0c789af57f2d65bfec0f6', '0xe3ec824c6d846fbc5c8eef622acfd295ef0fadef7585d8f46e4f6c658b48085e', '0x00000000000000000000000079dbc666f24349bf8f2cc4ef10269b6f60a8fd14', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000c9cd71f6c4790f9a384d0588a06722bc0bba782556a165a2b961efb7cedbd29500000000000000000000000000000000000000000000000000000000140b7ef7000000000000000000000000000000000000000000000000000000002e9db6260000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 678, 'sender_address': '0x4d97dcd97ec945f40cf65f87097ace5ea0476045', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20', 'erc1155', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4d97dcd97ec945f40cf65f87097ace5ea0476045.png', 'raw_log_topics': ['0xc3d58168c5ae7397731d063d5bbf3d657854427343f4c083240f7aacaa2d0f62', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0xb702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b8000000000000000000000000000000000000000000000000000000003c627de6', 'decoded': {'name': 'TransferSingle', 'signature': 'TransferSingle(indexed address _operator, indexed address _from, indexed address _to, uint256 _id, uint256 _amount)', 'params': [{'name': '_operator', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'}, {'name': '_to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4f46886de33690f98ca4e569d57704b476ee9136'}, {'name': '_id', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '82778075240613754181663052787751937303431519174245552377929584495420563833016'}, {'name': '_amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1013087718'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 679, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0xd0a08e8c493f9c94f29311604c9de1b4e8c8d4c06bd0c789af57f2d65bfec0f6', '0xd8f91e0b08616dc41e813116f10f9db73b5a27bdd2d759f73d010b6be4c05006', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136', '0x0000000000000000000000004bfb41d5b3570defd03c39a9a4d8de6bd8b8982e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b8000000000000000000000000000000000000000000000000000000002245cdbf000000000000000000000000000000000000000000000000000000003c627de60000000000000000000000000000000000000000000000000000000000000000'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 680, 'sender_address': '0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x4bfb41d5b3570defd03c39a9a4d8de6bd8b8982e.png', 'raw_log_topics': ['0x63bf4d16b7fa898ef4c4b2b6d90fd201e9c56313b65638af6088d149d2ce956c', '0xd8f91e0b08616dc41e813116f10f9db73b5a27bdd2d759f73d010b6be4c05006', '0x0000000000000000000000004f46886de33690f98ca4e569d57704b476ee9136'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000b702baf23ed9899d956e195b6e26e4d3b0be7574de88849dfc8e686ef710c0b8000000000000000000000000000000000000000000000000000000002245cdbf000000000000000000000000000000000000000000000000000000003c627de6'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 79, 'tx_hash': '0x432a5943288d4b7728c4ca3c96920b0a53fce65c7841ab03239055024e41c594', 'log_offset': 681, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000125914b1d921d83367a0478f4ea6447aff2ee9da', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000049682f60ffb40000000000000000000000000000000000000000000000007a635915248a85feba000000000000000000000000000000000000000000001e50bb781b6e0d4e14a700000000000000000000000000000000000000000000007a630facf529864aba000000000000000000000000000000000000000000001e50bbc1839d6e4dc8a7', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x125914b1d921d83367a0478f4ea6447aff2ee9da'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '20662226000000000'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2257661553321934454458'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143160242589227901916327'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2257640891095934454458'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143160263251453901916327'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765,40,0x0000000000000000000000000000000000000000,0xf724371bd088f6268b9943373a5ab8b334ced809,******************************************,0,1036240,1026923,36887500053,37880622216926919,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0075119869757085105,$0.01,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 249, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d52cca987aa10e45810cfac0a6971674b82d5564', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 250, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000d52cca987aa10e45810cfac0a6971674b82d5564', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 251, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000d52cca987aa10e45810cfac0a6971674b82d5564', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001298ed6', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '19500758'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 252, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000375ad58c59b02237d04093b8b4cb1d6b6d21ae71', '0x0000000000000000000000003eaf75a57009c7ccf53844f57da443dc55be87a7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 253, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000375ad58c59b02237d04093b8b4cb1d6b6d21ae71', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 254, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000375ad58c59b02237d04093b8b4cb1d6b6d21ae71', '0x0000000000000000000000003eaf75a57009c7ccf53844f57da443dc55be87a7'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000004c4b40', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 255, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000b2e31a43f468f4249e1cf232c7da2a39dc2c1356', '0x000000000000000000000000722ef6e217fa2000161551640a622e8d2316c080'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 256, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000b2e31a43f468f4249e1cf232c7da2a39dc2c1356', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 257, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000b2e31a43f468f4249e1cf232c7da2a39dc2c1356', '0x000000000000000000000000722ef6e217fa2000161551640a622e8d2316c080'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000c380d40', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '205000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 258, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c43e80405c61f5fcba04efcbe9e4e34a4fcfc6fe', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 259, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000c43e80405c61f5fcba04efcbe9e4e34a4fcfc6fe', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 260, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c43e80405c61f5fcba04efcbe9e4e34a4fcfc6fe', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000795dc1', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '7953857'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 261, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000be59c95e8b7de37960e7dd4b051258c6b46677cb', '0x000000000000000000000000d52ece95defa01b21da715dd62207cd92a49d5e5'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbe59c95e8b7de37960e7dd4b051258c6b46677cb'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 262, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000be59c95e8b7de37960e7dd4b051258c6b46677cb', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbe59c95e8b7de37960e7dd4b051258c6b46677cb'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 263, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000be59c95e8b7de37960e7dd4b051258c6b46677cb', '0x000000000000000000000000d52ece95defa01b21da715dd62207cd92a49d5e5'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000002dc6c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbe59c95e8b7de37960e7dd4b051258c6b46677cb'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 264, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000916d579c432f3d5d26d351a8d3d16606a7727cd0', '0x0000000000000000000000006322ee790ac0ad44267c38433fa00062750d6a47'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 265, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000916d579c432f3d5d26d351a8d3d16606a7727cd0', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 266, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000916d579c432f3d5d26d351a8d3d16606a7727cd0', '0x0000000000000000000000006322ee790ac0ad44267c38433fa00062750d6a47'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000f4240', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 267, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000005e1e7d81152e11f55020135b515a06e2860d0819', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x5e1e7d81152e11f55020135b515a06e2860d0819'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 268, 'sender_address': '0xaa1a12d10df6e029866adcda26b0557aab417b99', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xaa1a12d10df6e029866adcda26b0557aab417b99.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000005e1e7d81152e11f55020135b515a06e2860d0819', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000002aea540', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x5e1e7d81152e11f55020135b515a06e2860d0819'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '45000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 269, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000007bc64aedf392b698b3feeea43cac233e75656750', '0x0000000000000000000000005ec60740b588542ccadb5a4039270928c6ac6a5a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 270, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000007bc64aedf392b698b3feeea43cac233e75656750', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 271, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000007bc64aedf392b698b3feeea43cac233e75656750', '0x0000000000000000000000005ec60740b588542ccadb5a4039270928c6ac6a5a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000895440', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '9000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 272, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000962a462d954f14c9c9a60eba2e8b48b62c61d438', '0x00000000000000000000000034e255c7920b6f4aabb0d7d1de5eb25f2f7d9042'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 273, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000962a462d954f14c9c9a60eba2e8b48b62c61d438', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 274, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000962a462d954f14c9c9a60eba2e8b48b62c61d438', '0x00000000000000000000000034e255c7920b6f4aabb0d7d1de5eb25f2f7d9042'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000002dc6c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 275, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f8985061b11542722d40094539afb866ff3bb0f3', '0x0000000000000000000000009db189245a7f5edcf0a97480587297ce276b40aa'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 276, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000f8985061b11542722d40094539afb866ff3bb0f3', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 277, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f8985061b11542722d40094539afb866ff3bb0f3', '0x0000000000000000000000009db189245a7f5edcf0a97480587297ce276b40aa'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000007bfa480', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '130000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 278, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008d29c0796e1e920d2a3985478a7bfdc23b12ec6c', '0x000000000000000000000000512a321f6d9df58fa11238ae24ef718f3cf72323'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 279, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008d29c0796e1e920d2a3985478a7bfdc23b12ec6c', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 280, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008d29c0796e1e920d2a3985478a7bfdc23b12ec6c', '0x000000000000000000000000512a321f6d9df58fa11238ae24ef718f3cf72323'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000005b8d80', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '6000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 281, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000392dc81173ad9e4929ff7d552d741a12ea2a977e', '0x000000000000000000000000688369410242753f009d52c819f68d064c983a6c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 282, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000392dc81173ad9e4929ff7d552d741a12ea2a977e', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 283, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000392dc81173ad9e4929ff7d552d741a12ea2a977e', '0x000000000000000000000000688369410242753f009d52c819f68d064c983a6c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000027849c58', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '663002200'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 284, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003f2245445c390c19918c0624abc38afe14fbf8d2', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3f2245445c390c19918c0624abc38afe14fbf8d2'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 285, 'sender_address': '0xaa1a12d10df6e029866adcda26b0557aab417b99', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xaa1a12d10df6e029866adcda26b0557aab417b99.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003f2245445c390c19918c0624abc38afe14fbf8d2', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000010289f00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x3f2245445c390c19918c0624abc38afe14fbf8d2'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '271097600'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 286, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000823c783658fad00f391fb13215c21e6be4e1ecd2', '0x000000000000000000000000475a6d55658322082efa1b20f85310fd13d8f147'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x823c783658fad00f391fb13215c21e6be4e1ecd2'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 287, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000823c783658fad00f391fb13215c21e6be4e1ecd2', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x823c783658fad00f391fb13215c21e6be4e1ecd2'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 288, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000823c783658fad00f391fb13215c21e6be4e1ecd2', '0x000000000000000000000000475a6d55658322082efa1b20f85310fd13d8f147'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000004b571c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x823c783658fad00f391fb13215c21e6be4e1ecd2'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '79000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 289, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001d990a079d0354eae179fd3590281901bd9e280f', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 290, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000001d990a079d0354eae179fd3590281901bd9e280f', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 291, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001d990a079d0354eae179fd3590281901bd9e280f', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000006f7cf0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '7306480'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 292, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c33eddff717554170f981c53e8a50fb39ffc5750', '0x0000000000000000000000000658768d1609c99aa5da7b778f6211b5fc9bc2a0'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 293, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000c33eddff717554170f981c53e8a50fb39ffc5750', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 294, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c33eddff717554170f981c53e8a50fb39ffc5750', '0x0000000000000000000000000658768d1609c99aa5da7b778f6211b5fc9bc2a0'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000002dc6c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 295, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000042ca0baf2af22114359aaef41020a1f0b8012007', '0x000000000000000000000000f1f8a58a3015640fed6e0acd8cec80bfd9841c8a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x42ca0baf2af22114359aaef41020a1f0b8012007'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf1f8a58a3015640fed6e0acd8cec80bfd9841c8a'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 296, 'sender_address': '0xaa1a12d10df6e029866adcda26b0557aab417b99', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xaa1a12d10df6e029866adcda26b0557aab417b99.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000042ca0baf2af22114359aaef41020a1f0b8012007', '0x000000000000000000000000f1f8a58a3015640fed6e0acd8cec80bfd9841c8a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000006052340', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x42ca0baf2af22114359aaef41020a1f0b8012007'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf1f8a58a3015640fed6e0acd8cec80bfd9841c8a'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '101000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 297, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000002731908d3767c3f0cf09e4fc30fe0d02c4e6a599', '0x000000000000000000000000be10d0d59749cf2b6f55688da70067f96712909f'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x2731908d3767c3f0cf09e4fc30fe0d02c4e6a599'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbe10d0d59749cf2b6f55688da70067f96712909f'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 298, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000002731908d3767c3f0cf09e4fc30fe0d02c4e6a599', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x2731908d3767c3f0cf09e4fc30fe0d02c4e6a599'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 299, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000002731908d3767c3f0cf09e4fc30fe0d02c4e6a599', '0x000000000000000000000000be10d0d59749cf2b6f55688da70067f96712909f'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000037a9cd380', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x2731908d3767c3f0cf09e4fc30fe0d02c4e6a599'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbe10d0d59749cf2b6f55688da70067f96712909f'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14942000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 300, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56', '0x000000000000000000000000932f2811007546d3eab3a936755c42de188200f2'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x932f2811007546d3eab3a936755c42de188200f2'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 301, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000f2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 302, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56', '0x000000000000000000000000932f2811007546d3eab3a936755c42de188200f2'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000055d4a80', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf2a674e9d98b0fd19c4d1c2f6737c82aaa3cdf56'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x932f2811007546d3eab3a936755c42de188200f2'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '90000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 303, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f9f5bae923add9448c5336dc69375522fc92a8f1', '0x000000000000000000000000f2be6d990015e00f4d882a1d9e3d1c3b52849fa6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf9f5bae923add9448c5336dc69375522fc92a8f1'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 304, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000f9f5bae923add9448c5336dc69375522fc92a8f1', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf9f5bae923add9448c5336dc69375522fc92a8f1'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 305, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f9f5bae923add9448c5336dc69375522fc92a8f1', '0x000000000000000000000000f2be6d990015e00f4d882a1d9e3d1c3b52849fa6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000015fe7e40', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf9f5bae923add9448c5336dc69375522fc92a8f1'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '369000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 306, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001ec2829fed7f49303bf4073eb9d63fb8d2dba3e6', '0x00000000000000000000000096793d171931c686e9fd2ac9f09e01e708b3cc56'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 307, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000001ec2829fed7f49303bf4073eb9d63fb8d2dba3e6', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 308, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001ec2829fed7f49303bf4073eb9d63fb8d2dba3e6', '0x00000000000000000000000096793d171931c686e9fd2ac9f09e01e708b3cc56'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000b1eb7c40', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2985000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 309, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000001e626bf169f151e080a8b2e39ed19ef07e75ebb', '0x000000000000000000000000a4612f5bfd7e71935c1e1089f072d5ffbac862a6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 310, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000001e626bf169f151e080a8b2e39ed19ef07e75ebb', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 311, 'sender_address': '0x007f7802a3f7285d74f9cb2b62734d803a0cb778', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'W\u200bE\u200bT\u200bH', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'W\u200bE\u200bT\u200bH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x007f7802a3f7285d74f9cb2b62734d803a0cb778.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000001e626bf169f151e080a8b2e39ed19ef07e75ebb', '0x000000000000000000000000a4612f5bfd7e71935c1e1089f072d5ffbac862a6'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000016bc', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5820'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 312, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000086befee1e579bdb3171cd4b0b76fb6b32d648700', '0x000000000000000000000000e58adf7a5e1ea7537df516512859abbbc5e80d21'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x86befee1e579bdb3171cd4b0b76fb6b32d648700'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xe58adf7a5e1ea7537df516512859abbbc5e80d21'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 313, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000086befee1e579bdb3171cd4b0b76fb6b32d648700', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x86befee1e579bdb3171cd4b0b76fb6b32d648700'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 314, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000086befee1e579bdb3171cd4b0b76fb6b32d648700', '0x000000000000000000000000e58adf7a5e1ea7537df516512859abbbc5e80d21'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000a3140c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x86befee1e579bdb3171cd4b0b76fb6b32d648700'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xe58adf7a5e1ea7537df516512859abbbc5e80d21'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '171000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 315, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cbfc0446a94585cbb23e7ac73723b79777495e22', '0x000000000000000000000000442efb53898b6347211f2430fe32e1b177aa7868'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcbfc0446a94585cbb23e7ac73723b79777495e22'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 316, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000cbfc0446a94585cbb23e7ac73723b79777495e22', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcbfc0446a94585cbb23e7ac73723b79777495e22'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 317, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cbfc0446a94585cbb23e7ac73723b79777495e22', '0x000000000000000000000000442efb53898b6347211f2430fe32e1b177aa7868'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000002dc6c0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcbfc0446a94585cbb23e7ac73723b79777495e22'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 318, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000094ccf107532bb77318793a1bf741d2d6aad05bf8', '0x000000000000000000000000bbfdb5aec3136ba283520b72d73b7342fe0e9151'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 319, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000094ccf107532bb77318793a1bf741d2d6aad05bf8', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 320, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000094ccf107532bb77318793a1bf741d2d6aad05bf8', '0x000000000000000000000000bbfdb5aec3136ba283520b72d73b7342fe0e9151'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000f4268', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1000040'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 321, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000dd4f9dc199df990f169bfbe33134bc1b61406b01', '0x000000000000000000000000ea6c64b958ef94cf47ad5572aeb8c80ffcff6641'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 322, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000dd4f9dc199df990f169bfbe33134bc1b61406b01', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 323, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000dd4f9dc199df990f169bfbe33134bc1b61406b01', '0x000000000000000000000000ea6c64b958ef94cf47ad5572aeb8c80ffcff6641'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000002c8200a', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '46669834'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 324, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003494c023edddfed5769f8e2152b7cc6f47f15160', '0x000000000000000000000000dcd2b96814ca908c6ea45a83cf3874f5853b8fde'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 325, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000003494c023edddfed5769f8e2152b7cc6f47f15160', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 326, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003494c023edddfed5769f8e2152b7cc6f47f15160', '0x000000000000000000000000dcd2b96814ca908c6ea45a83cf3874f5853b8fde'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000563267', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5648999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 327, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004b00331be4a8d4fbf152c3e10c97818f417ae67c', '0x0000000000000000000000005d0ca025813c3cde754272fc70f54d9ce74b87cc'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 328, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000004b00331be4a8d4fbf152c3e10c97818f417ae67c', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 329, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004b00331be4a8d4fbf152c3e10c97818f417ae67c', '0x0000000000000000000000005d0ca025813c3cde754272fc70f54d9ce74b87cc'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000004e8270', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5145200'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 330, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006ae26b5843544defc1a3ae8e7aa882d0d7487e16', '0x000000000000000000000000996a4605a3a3cfec7c05179565d7bf747302549d'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6ae26b5843544defc1a3ae8e7aa882d0d7487e16'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 331, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000006ae26b5843544defc1a3ae8e7aa882d0d7487e16', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6ae26b5843544defc1a3ae8e7aa882d0d7487e16'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 332, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006ae26b5843544defc1a3ae8e7aa882d0d7487e16', '0x000000000000000000000000996a4605a3a3cfec7c05179565d7bf747302549d'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000002da0e3', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6ae26b5843544defc1a3ae8e7aa882d0d7487e16'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2990307'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 333, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cf1f4bee37f1affb5c61f52b91eaeeb13f99fd7d', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 334, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000cf1f4bee37f1affb5c61f52b91eaeeb13f99fd7d', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 335, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cf1f4bee37f1affb5c61f52b91eaeeb13f99fd7d', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000067ed33', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '6810931'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 336, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008ee1e95b0438d01ca82c9d89816292932f4c0aad', '0x0000000000000000000000002c766dc4b7bc56a167b20aef9180d885606b12f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 337, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008ee1e95b0438d01ca82c9d89816292932f4c0aad', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 338, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008ee1e95b0438d01ca82c9d89816292932f4c0aad', '0x0000000000000000000000002c766dc4b7bc56a167b20aef9180d885606b12f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000002aea540', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '45000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 339, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a5d4d3f008705e51c29375d2eaab48d4eda1b434', '0x000000000000000000000000ce55cce7d8981054f4f150084fd1681a267ec21b'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa5d4d3f008705e51c29375d2eaab48d4eda1b434'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 340, 'sender_address': '0xaa1a12d10df6e029866adcda26b0557aab417b99', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xaa1a12d10df6e029866adcda26b0557aab417b99.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a5d4d3f008705e51c29375d2eaab48d4eda1b434', '0x000000000000000000000000ce55cce7d8981054f4f150084fd1681a267ec21b'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000f4240', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa5d4d3f008705e51c29375d2eaab48d4eda1b434'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 341, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000ab9c8eb4359bf33ff889ded3702a5dbcf2b4e9f7', '0x0000000000000000000000006237ef12939a363527b53f56f344cdcec4a0459d'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 342, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000ab9c8eb4359bf33ff889ded3702a5dbcf2b4e9f7', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 343, 'sender_address': '0x007f7802a3f7285d74f9cb2b62734d803a0cb778', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'W\u200bE\u200bT\u200bH', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'W\u200bE\u200bT\u200bH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x007f7802a3f7285d74f9cb2b62734d803a0cb778.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000ab9c8eb4359bf33ff889ded3702a5dbcf2b4e9f7', '0x0000000000000000000000006237ef12939a363527b53f56f344cdcec4a0459d'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000001284', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4740'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 344, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009cd47a5d53423006d2b9fffdffb09bdebb26ae72', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 345, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000009cd47a5d53423006d2b9fffdffb09bdebb26ae72', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 346, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009cd47a5d53423006d2b9fffdffb09bdebb26ae72', '0x0000000000000000000000004d5f601ec28632dc5c8ddf361d16a4565802702e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000b53518', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11875608'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 347, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004a51a521be643a28213349bf5986035bd7e9cba1', '0x000000000000000000000000354eb6ba32535d458e16c3a409be43a2c41c7048'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4a51a521be643a28213349bf5986035bd7e9cba1'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x354eb6ba32535d458e16c3a409be43a2c41c7048'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 348, 'sender_address': '0xaa1a12d10df6e029866adcda26b0557aab417b99', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xaa1a12d10df6e029866adcda26b0557aab417b99.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000004a51a521be643a28213349bf5986035bd7e9cba1', '0x000000000000000000000000354eb6ba32535d458e16c3a409be43a2c41c7048'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000003ad9eb0', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4a51a521be643a28213349bf5986035bd7e9cba1'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x354eb6ba32535d458e16c3a409be43a2c41c7048'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '61710000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 349, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000069b66d3addce0778ad69d7bc9655e29f0f9afa5e', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x69b66d3addce0778ad69d7bc9655e29f0f9afa5e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 350, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000069b66d3addce0778ad69d7bc9655e29f0f9afa5e', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x69b66d3addce0778ad69d7bc9655e29f0f9afa5e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 351, 'sender_address': '0x3dee1c420d60ed04247870ddee740fba96a82455', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDС.e', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDС.e', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3dee1c420d60ed04247870ddee740fba96a82455.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000069b66d3addce0778ad69d7bc9655e29f0f9afa5e', '0x0000000000000000000000002204e6b582447399eff99431d031708ed2a106b6'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000003bb5ed76', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x69b66d3addce0778ad69d7bc9655e29f0f9afa5e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1001778550'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 352, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000058ba1669caecd45edcae56089c0d0c48178aef5e', '0x0000000000000000000000008b7acfa139f52859b61880237aa50d6cc6f3bdee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x58ba1669caecd45edcae56089c0d0c48178aef5e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 353, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x00000000000000000000000058ba1669caecd45edcae56089c0d0c48178aef5e', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x58ba1669caecd45edcae56089c0d0c48178aef5e'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 354, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x00000000000000000000000058ba1669caecd45edcae56089c0d0c48178aef5e', '0x0000000000000000000000008b7acfa139f52859b61880237aa50d6cc6f3bdee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000005f5e100', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x58ba1669caecd45edcae56089c0d0c48178aef5e'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 355, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c5c4b1883619d0e9f53359924e7e0403ff1f7c10', '0x000000000000000000000000f215c33aedc703eabe769be4ab5e1c26d4529703'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 356, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000c5c4b1883619d0e9f53359924e7e0403ff1f7c10', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 357, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000c5c4b1883619d0e9f53359924e7e0403ff1f7c10', '0x000000000000000000000000f215c33aedc703eabe769be4ab5e1c26d4529703'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000121eac00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '304000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 358, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008c7572ae7ab9ce9a561ea00338b57fac493559ad', '0x0000000000000000000000005426fd2d658f2a022331399ead387f38e981193f'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 359, 'sender_address': '******************************************', 'sender_address_label': '(PoS) Tether USD (USDT)', 'sender_factory_address': None, 'sender_name': '(PoS) Tether USD', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008c7572ae7ab9ce9a561ea00338b57fac493559ad', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 360, 'sender_address': '0x5e0962bfb12db611ad35a675f3b032a3891fb81d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'UЅDT', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'UЅDT', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x5e0962bfb12db611ad35a675f3b032a3891fb81d.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008c7572ae7ab9ce9a561ea00338b57fac493559ad', '0x0000000000000000000000005426fd2d658f2a022331399ead387f38e981193f'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000989680', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 361, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000555ccbb526921d364726085727a406299f1236e8', '0x000000000000000000000000932f2811007546d3eab3a936755c42de188200f2'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x555ccbb526921d364726085727a406299f1236e8'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x932f2811007546d3eab3a936755c42de188200f2'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 362, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000555ccbb526921d364726085727a406299f1236e8', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x555ccbb526921d364726085727a406299f1236e8'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 363, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000555ccbb526921d364726085727a406299f1236e8', '0x000000000000000000000000932f2811007546d3eab3a936755c42de188200f2'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000002625a00', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x555ccbb526921d364726085727a406299f1236e8'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x932f2811007546d3eab3a936755c42de188200f2'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '40000000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 364, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000996a54efae911891f0f7c75c3750ce6c7cfc549d', '0x000000000000000000000000898fbb8d2c0debc4297acf6dc998cbdaf4f80304'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x996a54efae911891f0f7c75c3750ce6c7cfc549d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x898fbb8d2c0debc4297acf6dc998cbdaf4f80304'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 365, 'sender_address': '0x8f3cf7ad23cd3cadbd9735aff958023239c6a063', 'sender_address_label': '(PoS) Dai Stablecoin (DAI)', 'sender_factory_address': None, 'sender_name': '(PoS) Dai Stablecoin', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'DAI', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x8f3cf7ad23cd3cadbd9735aff958023239c6a063.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000996a54efae911891f0f7c75c3750ce6c7cfc549d', '0x000000000000000000000000af6be838b0d1486dfe522c7b610579d57cde871c'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x996a54efae911891f0f7c75c3750ce6c7cfc549d'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 366, 'sender_address': '0x136be698a1a6e980c96b8e4a62af9c5c1f558b50', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': '(РоЅ) Dаі Ѕtаbleсоіn', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'DАІ', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x136be698a1a6e980c96b8e4a62af9c5c1f558b50.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000996a54efae911891f0f7c75c3750ce6c7cfc549d', '0x000000000000000000000000898fbb8d2c0debc4297acf6dc998cbdaf4f80304'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001c22260', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x996a54efae911891f0f7c75c3750ce6c7cfc549d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x898fbb8d2c0debc4297acf6dc998cbdaf4f80304'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29500000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 40, 'tx_hash': '0x5d13fd40dc217cf91e653c280f6d34d4438ec19007d25b1e9622ec0277433765', 'log_offset': 367, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000f724371bd088f6268b9943373a5ab8b334ced809', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000869439a3be82ca000000000000000000000000000000000000000000000005773b3dd847d0e541000000000000000000000000000000000000000000001e50b805689432153eb600000000000000000000000000000000000000000000000576b4a99ea4126277000000000000000000000000000000000000000000001e50b88bfccdd5d3c180', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf724371bd088f6268b9943373a5ab8b334ced809'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '37880622160446154'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100825249081814213953'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159994131649687535286'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100787368459653767799'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143160032012271847981440'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f,38,0x0000000000000000000000000000000000000000,0xf70da97812cb96acdf810712aa562db8dfa3dbef,0xeeeeee9ec4769a09a76a83c7bc42b185872860ee,0,749556,383576,38396840167,14728106363897192,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0029206844214139426,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 221, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f70da97812cb96acdf810712aa562db8dfa3dbef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae89fa', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf70da97812cb96acdf810712aa562db8dfa3dbef'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11438586'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 222, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x0000000000000000000000000000000000001ff3684f28c67538d4d072c22734'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae89fa', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000001ff3684f28c67538d4d072c22734'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11438586'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 223, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae89fa', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11438586'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 224, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae879b', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11437979'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 225, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae89fa', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11438586'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 226, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751', '0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000001', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x36e6fb1846c66898d28800497a2620d374ba5c80'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 227, 'sender_address': '0xeecb5db986c20a8c88d8332e7e252a9671565751', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xeecb5db986c20a8c88d8332e7e252a9671565751.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffff5178650000000000000000000000000000000000000000000000000000000000ae89fa00000000000000000000000000000000000000010001b83e9d79fc836bcf72f3000000000000000000000000000000000000000000000000000014bf62fa96060000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-11437979'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '11438586'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '79230241510562311184946524915'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '22812231898630'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 228, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000031db81aec3572b297', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 229, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000ae879b', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11437979'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 230, 'sender_address': '0xa374094527e1673a86de625aa59517c5de346d32', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xa374094527e1673a86de625aa59517c5de346d32.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffce247e513ca8d4d690000000000000000000000000000000000000000000000000000000000ae879b00000000000000000000000000000000000000000000077b6bfa51d2efa67c7300000000000000000000000000000000000000000000000016ae3c8d4cd89200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8985', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-57481723445754638999'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '11437979'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '35333295522426432945267'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '1634310292359385600'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-292475'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 231, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000031db81aec3572b297000000000000000000000000000000000000000000ead493b506587540b3e62d0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ead490974e3d890b4133960000000000000000000000000000000000000000000000031db81aec3572b297', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '283892508199781058315806253'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '283892450718057612561167254'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 232, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000031db81aec3572b297', 'decoded': {'name': 'Withdrawal', 'signature': 'Withdrawal(indexed address src, uint256 wad)', 'params': [{'name': 'src', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 233, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000031db81aec3572b2970000000000000000000000000000000000000000000000031db81aec3572b297000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000031db81aec3572b297', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 234, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000c6f3ae01982f3d23dabbd9556cebc087381bdce6'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000031db81aec3572b2970000000000000000000000000000000000000000000000031db81aec3572b297000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000031db81aec3572b297', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xc6f3ae01982f3d23dabbd9556cebc087381bdce6'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '57481723445754638999'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 38, 'tx_hash': '0x7df81415a34d9179d046cec8530f7e96f19a355c566b19f1a9722b3768c7fb4f', 'log_offset': 235, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000f70da97812cb96acdf810712aa562db8dfa3dbef', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000003453227ac6dc80000000000000000000000000000000000000000000000053756c09fe91404833000000000000000000000000000000000000000000001e50b7aad46164b99f460000000000000000000000000000000000000000000000537537b6dc16796bb3000000000000000000000000000000000000000000001e50b7df2783df807bc6', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf70da97812cb96acdf810712aa562db8dfa3dbef'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '14728106342800512'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1539540906906778355763'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159968635955868966726'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1539526178800435555251'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159983364062211767238'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526,36,0x0000000000000000000000000000000000000000,0xf70da97812cb96acdf810712aa562db8dfa3dbef,0xeeeeee9ec4769a09a76a83c7bc42b185872860ee,0,805269,539526,38396840167,20716093587940842,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.004108143322699488,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 185, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000f70da97812cb96acdf810712aa562db8dfa3dbef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001befe77', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf70da97812cb96acdf810712aa562db8dfa3dbef'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29294199'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 186, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x0000000000000000000000000000000000001ff3684f28c67538d4d072c22734'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001befe77', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000001ff3684f28c67538d4d072c22734'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29294199'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 187, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001befe77', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29294199'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 188, 'sender_address': '0xeecb5db986c20a8c88d8332e7e252a9671565751', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xeecb5db986c20a8c88d8332e7e252a9671565751.png', 'raw_log_topics': ['0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000001'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 189, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001bef89a', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29292698'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 190, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001befe77', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '29294199'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 191, 'sender_address': '0x3c499c542cef5e3811e1192ce70d8cc03d5c3359', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x3c499c542cef5e3811e1192ce70d8cc03d5c3359.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000eecb5db986c20a8c88d8332e7e252a9671565751', '0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000004', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeecb5db986c20a8c88d8332e7e252a9671565751'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x36e6fb1846c66898d28800497a2620d374ba5c80'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 192, 'sender_address': '0xeecb5db986c20a8c88d8332e7e252a9671565751', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xeecb5db986c20a8c88d8332e7e252a9671565751.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffe4107660000000000000000000000000000000000000000000000000000000001befe7700000000000000000000000000000000000000010001afd5053d13381aaec158000000000000000000000000000000000000000000000000000014bf62fa96060000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-29292698'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '29294199'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '79230201783752421186221293912'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '22812231898630'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 193, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x000000000000000000000000000000000000000000000006b602c8fee06c494d', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '123795730504126056781'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 194, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000177dba5', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '24632229'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 195, 'sender_address': '0xa374094527e1673a86de625aa59517c5de346d32', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xa374094527e1673a86de625aa59517c5de346d32.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffff949fd37011f93b6b3000000000000000000000000000000000000000000000000000000000177dba500000000000000000000000000000000000000000000077b64495b83102a496100000000000000000000000000000000000000000000000016ae3c8d4cd89200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8984', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-123795730504126056781'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '24632229'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '35332741308849354066273'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '1634310292359385600'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-292476'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 196, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000cd353f79d9fade311fc3119b841e1f456b54e858'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000471cf5', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd353f79d9fade311fc3119b841e1f456b54e858'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660469'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 197, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cd353f79d9fade311fc3119b841e1f456b54e858', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x000000000000000000000000000000000000000000000001450cfd8f950f7ce4', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd353f79d9fade311fc3119b841e1f456b54e858'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '23422374555170077924'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 198, 'sender_address': '0xcd353f79d9fade311fc3119b841e1f456b54e858', 'sender_address_label': None, 'sender_factory_address': '0xc35dadb65012ec5796536bd9864ed8773abc74c4', 'sender_name': 'SushiSwap LP Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'SLP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xcd353f79d9fade311fc3119b841e1f456b54e858.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '0x0000000000000000000000000000000000000000000040de320b6c24928e46700000000000000000000000000000000000000000000000000000000e2667f713', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '306330238182851787769456'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '60773889811'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 199, 'sender_address': '0xcd353f79d9fade311fc3119b841e1f456b54e858', 'sender_address_label': None, 'sender_factory_address': '0xc35dadb65012ec5796536bd9864ed8773abc74c4', 'sender_name': 'SushiSwap LP Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'SLP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xcd353f79d9fade311fc3119b841e1f456b54e858.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000471cf5000000000000000000000000000000000000000000000001450cfd8f950f7ce40000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '4660469'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '23422374555170077924'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 200, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000000d500b1d8e8ef31e21c99d1db9a6444d3adf1270', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x000000000000000000000000000000000000000000000007fb0fc68e757bc631000000000000000000000000000000000000000000ead49bb0161f03b62fac5e0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ead493b506587540b3e62d000000000000000000000000000000000000000000000007fb0fc68e757bc631', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '283892655417886117611940958'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '283892508199781058315806253'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 201, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7'], 'raw_log_data': '0x000000000000000000000000000000000000000000000007fb0fc68e757bc631', 'decoded': {'name': 'Withdrawal', 'signature': 'Withdrawal(indexed address src, uint256 wad)', 'params': [{'name': 'src', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 202, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000a3d370e8a4180828f6756cb8dce359cf21d9d6f7', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee'], 'raw_log_data': '0x000000000000000000000000000000000000000000000007fb0fc68e757bc631000000000000000000000000000000000000000000000007fb0fc68e757bc63100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000007fb0fc68e757bc631', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa3d370e8a4180828f6756cb8dce359cf21d9d6f7'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 203, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0xe6497e3ee548a3372136af2fcb0696db31fc6cf20260707645068bd3fe97f3c4', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000eeeeee9ec4769a09a76a83c7bc42b185872860ee', '0x00000000000000000000000052583b4b3bcc8b564016a1093bc57107d2a18fc4'], 'raw_log_data': '0x000000000000000000000000000000000000000000000007fb0fc68e757bc631000000000000000000000000000000000000000000000007fb0fc68e757bc6310000000000000000000000000000000000000000000000004af2205ae423b62c00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000084601e6e9599f7c5d', 'decoded': {'name': 'LogTransfer', 'signature': 'LogTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xeeeeee9ec4769a09a76a83c7bc42b185872860ee'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x52583b4b3bcc8b564016a1093bc57107d2a18fc4'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '147218105059296134705'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '5400414477917337132'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '152618519537213471837'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 36, 'tx_hash': '0x1f4acae3db2786c13c6f099acb3bf1c06742228ed31e1d4654a8790ee1cb0526', 'log_offset': 204, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000f70da97812cb96acdf810712aa562db8dfa3dbef', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000049992d65ac9c2000000000000000000000000000000000000000000000005375f08db06e997bde000000000000000000000000000000000000000000001e50b72650af8a8fab5600000000000000000000000000000000000000000000005375a6f48308ecdfbe000000000000000000000000000000000000000000001e50b76fe9dcf03c4776', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf70da97812cb96acdf810712aa562db8dfa3dbef'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '20716093558266912'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1539578206403650223070'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159931336459050527574'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1539557490310091956158'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159952052552608794486'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x94d99c2cf15d616d2730ffe40c6c5dd30cfd5ee0de5f0f45475033dc6db7f10c,20,0x0000000000000000000000000000000000000000,0x9bc2734d5dbcf343e1fc68b0618d99d7713376a9,0x2791bca1f2de4661ed88a30c99a7a9449aa84174,0,85293,63552,41029000053,2607475011368256,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x94d99c2cf15d616d2730ffe40c6c5dd30cfd5ee0de5f0f45475033dc6db7f10c'}]",,USD Coin (PoS) (USDC),"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0005170801633805045,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 20, 'tx_hash': '0x94d99c2cf15d616d2730ffe40c6c5dd30cfd5ee0de5f0f45475033dc6db7f10c', 'log_offset': 98, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009bc2734d5dbcf343e1fc68b0618d99d7713376a9', '0x000000000000000000000000bc40193acbdbd17f91a4671e2de8acdc100f8d05'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000003356170', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9bc2734d5dbcf343e1fc68b0618d99d7713376a9'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xbc40193acbdbd17f91a4671e2de8acdc100f8d05'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '53830000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 20, 'tx_hash': '0x94d99c2cf15d616d2730ffe40c6c5dd30cfd5ee0de5f0f45475033dc6db7f10c', 'log_offset': 99, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x0000000000000000000000009bc2734d5dbcf343e1fc68b0618d99d7713376a9', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000009437c159adf80000000000000000000000000000000000000000000000032a5f3218d7ed7a1ec000000000000000000000000000000000000000000001e50b59ea830ef5c59b2000000000000000000000000000000000000000000000032a5e9de11693cc26c000000000000000000000000000000000000000000001e50b5a7ebad04f73932', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x9bc2734d5dbcf343e1fc68b0618d99d7713376a9'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2607475007872896'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '934295142012678545900'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159821094481433483698'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '934292534537670673004'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159823701956441356594'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x29b69b17870b8a23cd7c68b3ff5000e628f98e51e7618c88e5abaf814d208aa8,19,0x0000000000000000000000000000000000000000,0xa45a4590e8b448cce3e3c48c52abe6c0e39c1a43,0x2791bca1f2de4661ed88a30c99a7a9449aa84174,0,85293,63552,41029000053,2607475011368256,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x29b69b17870b8a23cd7c68b3ff5000e628f98e51e7618c88e5abaf814d208aa8'}]",,USD Coin (PoS) (USDC),"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0005170801633805045,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 19, 'tx_hash': '0x29b69b17870b8a23cd7c68b3ff5000e628f98e51e7618c88e5abaf814d208aa8', 'log_offset': 96, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a45a4590e8b448cce3e3c48c52abe6c0e39c1a43', '0x0000000000000000000000004859cc0bf94886493688774afe6aa2a61af85984'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001f76330', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa45a4590e8b448cce3e3c48c52abe6c0e39c1a43'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x4859cc0bf94886493688774afe6aa2a61af85984'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '32990000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 19, 'tx_hash': '0x29b69b17870b8a23cd7c68b3ff5000e628f98e51e7618c88e5abaf814d208aa8', 'log_offset': 97, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000a45a4590e8b448cce3e3c48c52abe6c0e39c1a43', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000009437c159adf8000000000000000000000000000000000000000000000002e75fbdf16c20d94e1000000000000000000000000000000000000000000001e50b59564b4d9c17a3200000000000000000000000000000000000000000000002e75f29b9aac72b561000000000000000000000000000000000000000000001e50b59ea830ef5c59b2', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa45a4590e8b448cce3e3c48c52abe6c0e39c1a43'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2607475007872896'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '857051861401069262049'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159818487006425610802'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '857049253926061389153'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159821094481433483698'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x9e0a59659ae34455aec24576c0b835328d7c3f0048cc6a7345fb717610971a82,18,0x0000000000000000000000000000000000000000,0xece6886c64c3ac8f83e302a6a71fcb015135d298,0x2791bca1f2de4661ed88a30c99a7a9449aa84174,0,85293,63552,41029000053,2607475011368256,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x9e0a59659ae34455aec24576c0b835328d7c3f0048cc6a7345fb717610971a82'}]",,USD Coin (PoS) (USDC),"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0005170801633805045,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 18, 'tx_hash': '0x9e0a59659ae34455aec24576c0b835328d7c3f0048cc6a7345fb717610971a82', 'log_offset': 94, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000ece6886c64c3ac8f83e302a6a71fcb015135d298', '0x000000000000000000000000068a3787f927620c1dbc1a630af03fe1a4786cf4'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000003419670', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xece6886c64c3ac8f83e302a6a71fcb015135d298'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x068a3787f927620c1dbc1a630af03fe1a4786cf4'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '54630000'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 18, 'tx_hash': '0x9e0a59659ae34455aec24576c0b835328d7c3f0048cc6a7345fb717610971a82', 'log_offset': 95, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000ece6886c64c3ac8f83e302a6a71fcb015135d298', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000009437c159adf800000000000000000000000000000000000000000000000300a04930318564338000000000000000000000000000000000000000000001e50b58c2138c4269ab200000000000000000000000000000000000000000000003009fb4f8702bb63b8000000000000000000000000000000000000000000001e50b59564b4d9c17a32', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xece6886c64c3ac8f83e302a6a71fcb015135d298'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2607475007872896'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '886165579019847091000'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159815879531417737906'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '886162971544839218104'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159818487006425610802'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269,17,0x0000000000000000000000000000000000000000,0xd90c0c4dddf406712efbe2926d6e6389c7402484,0x8017f085698af15c65b34dbefaf191b08586cd88,0,200000,147666,78000000171,11517948025250886,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.0022840880241378307,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 17, 'tx_hash': '0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269', 'log_offset': 89, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x000000000000000000000000000000000000000000000035125a265e4c13c6ef', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '978999847533638043375'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 17, 'tx_hash': '0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269', 'log_offset': 90, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x000000000000000000000000a374094527e1673a86de625aa59517c5de346d32'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000b9b760d', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa374094527e1673a86de625aa59517c5de346d32'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '194737677'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 17, 'tx_hash': '0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269', 'log_offset': 91, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x00000000000000000000000068b3465833fb72a70ecdf485e0e4c7bd8665fc45'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffff4f2fcbacba2', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x68b3465833fb72a70ecdf485e0e4c7bd8665fc45'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457583995762612292514'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 17, 'tx_hash': '0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269', 'log_offset': 92, 'sender_address': '0xa374094527e1673a86de625aa59517c5de346d32', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xa374094527e1673a86de625aa59517c5de346d32.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x00000000000000000000000068b3465833fb72a70ecdf485e0e4c7bd8665fc45', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffcaeda5d9a1b3ec3911000000000000000000000000000000000000000000000000000000000b9b760d00000000000000000000000000000000000000000000077b53b91abcfba8d83600000000000000000000000000000000000000000000000016ae3c8d4cd89200fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb8984', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x68b3465833fb72a70ecdf485e0e4c7bd8665fc45'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-978999847533638043375'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '194737677'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '35331547783728609155126'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '1634310292359385600'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-292476'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 17, 'tx_hash': '0xe58ea69908d7d84c8dfbbb24a265c03c2e8b30cd107d344cf2c58b83b67ac269', 'log_offset': 93, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000d90c0c4dddf406712efbe2926d6e6389c7402484', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000028eb83401a3728000000000000000000000000000000000000000000000066de15528a9bfc8c69000000000000000000000000000000000000000000001e50b56335b5840c638a000000000000000000000000000000000000000000000066ddec67075be25541000000000000000000000000000000000000000000001e50b58c2138c4269ab2', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xd90c0c4dddf406712efbe2926d6e6389c7402484'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '11517948017129256'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897570683124581174377'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159804361583400608650'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897559165176564045121'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159815879531417737906'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4,15,0x0000000000000000000000000000000000000000,0xd90c0c4dddf406712efbe2926d6e6389c7402484,0x8017f085698af15c65b34dbefaf191b08586cd88,0,600000,322144,78000000171,25127232055086624,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.004982902309589597,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 78, 'sender_address': '0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d.png', 'raw_log_topics': ['0x598b9f043c813aa6be3426ca60d1c65d17256312890be5118dab55b0775ebe2a'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000376'}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 79, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000ae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000643a82f1231e41ee58', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1848890604705261153880'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 80, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x000000000000000000000000ae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000015ebc834', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '367773748'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 81, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x000000000000000000000000f5b509bb0909a69b1c207e495f687a596c168e12'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffa428d687d8a', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf5b509bb0909a69b1c207e495f687a596c168e12'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584001601900150154'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 82, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000ae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d', '0x00000000000000000000000036e6fb1846c66898d28800497a2620d374ba5c80'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000beed', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x36e6fb1846c66898d28800497a2620d374ba5c80'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '48877'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 83, 'sender_address': '0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xae81fac689a1b4b1e06e7ef4a2ab4cd8ac0a087d.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x000000000000000000000000f5b509bb0909a69b1c207e495f687a596c168e12', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffff9bc57d0edce1be11a80000000000000000000000000000000000000000000000000000000015ebc83400000000000000000000000000000000000000000000077aeab9182d00bb06f200000000000000000000000000000000000000000000000033f94c1e122868bcfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffb897f', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xf5b509bb0909a69b1c207e495f687a596c168e12'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-1848890604705261153880'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '367773748'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '35323981733537213253362'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '3745108257172646076'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '-292481'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 15, 'tx_hash': '0x6298a3ca91d0b97e5f7b603ecbeec8216664dc59edf9db207b2173decbc126b4', 'log_offset': 84, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000d90c0c4dddf406712efbe2926d6e6389c7402484', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000005945159bc6b380000000000000000000000000000000000000000000000066de9695342f0ac671000000000000000000000000000000000000000000001e50b4e1f30bf285a0aa000000000000000000000000000000000000000000000066de3d501e934412f1000000000000000000000000000000000000000000001e50b53b38218e4c542a', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xd90c0c4dddf406712efbe2926d6e6389c7402484'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '25127232037368704'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897607066692660938353'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159767978015346499754'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897581939460623569649'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159793105247383868458'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0,13,0x0000000000000000000000000000000000000000,0xd90c0c4dddf406712efbe2926d6e6389c7402484,0x8017f085698af15c65b34dbefaf191b08586cd88,0,200000,137142,78000000171,10697076023451282,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.00212130348087109,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 66, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x000000000000000000000000cd353f79d9fade311fc3119b841e1f456b54e858'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000001209438', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd353f79d9fade311fc3119b841e1f456b54e858'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '18912312'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 67, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x0000000000000000000000001b02da8cb0d097eb8d57a175b88c7d8b47997506'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffffffffee5ae4e3cc', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1b02da8cb0d097eb8d57a175b88c7d8b47997506'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007837345178572'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 68, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000cd353f79d9fade311fc3119b841e1f456b54e858', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000052793910d7aa173e8', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xcd353f79d9fade311fc3119b841e1f456b54e858'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '95085502844681417704'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 69, 'sender_address': '0xcd353f79d9fade311fc3119b841e1f456b54e858', 'sender_address_label': None, 'sender_factory_address': '0xc35dadb65012ec5796536bd9864ed8773abc74c4', 'sender_name': 'SushiSwap LP Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'SLP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xcd353f79d9fade311fc3119b841e1f456b54e858.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '0x0000000000000000000000000000000000000000000040df771869b4279dc3540000000000000000000000000000000000000000000000000000000e2620da1e', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '306353660557406957847380'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '60769229342'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 70, 'sender_address': '0xcd353f79d9fade311fc3119b841e1f456b54e858', 'sender_address_label': None, 'sender_factory_address': '0xc35dadb65012ec5796536bd9864ed8773abc74c4', 'sender_name': 'SushiSwap LP Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'SLP', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0xcd353f79d9fade311fc3119b841e1f456b54e858.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x0000000000000000000000001b02da8cb0d097eb8d57a175b88c7d8b47997506', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000012094380000000000000000000000000000000000000000000000052793910d7aa173e80000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x1b02da8cb0d097eb8d57a175b88c7d8b47997506'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '18912312'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '95085502844681417704'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 13, 'tx_hash': '0xcf337df0a929d978c9559647d34567dc9ef3fbcc184366a9083c3eb91f04fde0', 'log_offset': 71, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000d90c0c4dddf406712efbe2926d6e6389c7402484', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000002600ef18104678000000000000000000000000000000000000000000000066df0f8a33d6dc4175000000000000000000000000000000000000000000001e50b468fe0c4c22773a000000000000000000000000000000000000000000000066dee98944becbfafd000000000000000000000000000000000000000000001e50b48efefb6432bdb2', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xd90c0c4dddf406712efbe2926d6e6389c7402484'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10697076015908472'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897641113068735578485'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159733931639295866682'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897630415992719670013'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159744628715311775154'}]}}]"
72876844,2025-06-17 11:01:22+00:00,0x76c0c109459ab89ea269d38013637be84760fb6736e29d8813162bf266537596,0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12,4,0x0000000000000000000000000000000000000000,0xd90c0c4dddf406712efbe2926d6e6389c7402484,0x8017f085698af15c65b34dbefaf191b08586cd88,0,200000,136205,78000000171,10623990023291055,True,137,matic-mainnet,"[{'label': 'PolygonScan', 'url': 'https://polygonscan.com/tx/0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12'}]",,,"{'contract_decimals': 18, 'contract_name': 'Polygon Ecosystem Token', 'contract_ticker_symbol': 'POL', 'contract_address': '0x455e53CBB86018Ac2B8092FdCd39d8444aFFC3F6', 'supports_erc': ['erc20'], 'logo_url': 'https://www.datocms-assets.com/86369/1677870347-property-1-polygon-zkevm-icon-white.svg'}",0.19830685284656668,0.002106810026192172,$0.00,0.0,$0.00,"[{'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 20, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x0000000000000000000000006e7a5fafcec6bb1e78bae2a1f0b612012bf14827'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000424a331', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '69509937'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 21, 'sender_address': '0x2791bca1f2de4661ed88a30c99a7a9449aa84174', 'sender_address_label': 'USD Coin (PoS) (USDC)', 'sender_factory_address': None, 'sender_name': 'USD Coin (PoS)', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x2791bca1f2de4661ed88a30c99a7a9449aa84174.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88', '0x000000000000000000000000a5e0829caced8ffdd4de3c43696c57f7d7a678ff'], 'raw_log_data': '0xffffffffffffffffffffffffffffffffffffffffffffffffffffff87fbedb136', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '115792089237316195423570985008687907853269984665640564039457584007397665255734'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 22, 'sender_address': '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270', 'sender_address_label': 'Polygon: WMATIC Token', 'sender_factory_address': None, 'sender_name': 'Wrapped Matic', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WMATIC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006e7a5fafcec6bb1e78bae2a1f0b612012bf14827', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x000000000000000000000000000000000000000000000012f14de116b9feabf3', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '349429194550845287411'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 23, 'sender_address': '0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827', 'sender_address_label': None, 'sender_factory_address': '0x5757371414417b8c6caad45baef941abc7d3ab32', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827.png', 'raw_log_topics': ['0x1c411e9a96e071241c2f21f7726b17ae89e3cab4c78be50e062b03a9fffbbad1'], 'raw_log_data': '0x00000000000000000000000000000000000000000001a269848e28eb9ba70ee70000000000000000000000000000000000000000000000000000005b41ad2423', 'decoded': {'name': 'Sync', 'signature': 'Sync(uint112 reserve0, uint112 reserve1)', 'params': [{'name': 'reserve0', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '1975895649584103295487719'}, {'name': 'reserve1', 'type': 'uint112', 'indexed': False, 'decoded': True, 'value': '391943889955'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 24, 'sender_address': '0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827', 'sender_address_label': None, 'sender_factory_address': '0x5757371414417b8c6caad45baef941abc7d3ab32', 'sender_name': 'Uniswap V2', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'UNI-V2', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x6e7a5fafcec6bb1e78bae2a1f0b612012bf14827.png', 'raw_log_topics': ['0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', '0x000000000000000000000000a5e0829caced8ffdd4de3c43696c57f7d7a678ff', '0x0000000000000000000000008017f085698af15c65b34dbefaf191b08586cd88'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000424a331000000000000000000000000000000000000000000000012f14de116b9feabf30000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, uint256 amount0In, uint256 amount1In, uint256 amount0Out, uint256 amount1Out, indexed address to)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xa5e0829caced8ffdd4de3c43696c57f7d7a678ff'}, {'name': 'amount0In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'amount1In', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '69509937'}, {'name': 'amount0Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '349429194550845287411'}, {'name': 'amount1Out', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x8017f085698af15c65b34dbefaf191b08586cd88'}]}}, {'block_height': 72876844, 'block_signed_at': '2025-06-17T11:01:22Z', 'tx_offset': 4, 'tx_hash': '0xac27e5c734f226f402a3017f60570699fef1c5145b68d5582ca27c228b945e12', 'log_offset': 25, 'sender_address': '0x0000000000000000000000000000000000001010', 'sender_address_label': 'Polygon: MATIC Token', 'sender_factory_address': None, 'sender_name': 'Matic Token', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'MATIC', 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/137/0x0000000000000000000000000000000000001010.png', 'raw_log_topics': ['0x4dfe1bbbcf077ddc3e01291eea2d5c70c2b422b415d95645b9adcfd678cb1d63', '0x0000000000000000000000000000000000000000000000000000000000001010', '0x000000000000000000000000d90c0c4dddf406712efbe2926d6e6389c7402484', '0x00000000000000000000000083d69448f88bf9c701c1b93f43e1f753d39b2632'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000025be766efd31e4000000000000000000000000000000000000000000000066dfa371f064ecdd98000000000000000000000000000000000000000000001e50b29ae96c3ba2101d000000000000000000000000000000000000000000000066df7db379f5efabb4000000000000000000000000000000000000000000001e50b2c0a7e2aa9f4201', 'decoded': {'name': 'LogFeeTransfer', 'signature': 'LogFeeTransfer(indexed address token, indexed address from, indexed address to, uint256 amount, uint256 input1, uint256 input2, uint256 output1, uint256 output2)', 'params': [{'name': 'token', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x0000000000000000000000000000000000001010'}, {'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0xd90c0c4dddf406712efbe2926d6e6389c7402484'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '0x83d69448f88bf9c701c1b93f43e1f753d39b2632'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '10623990015799780'}, {'name': 'input1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897682744687335366040'}, {'name': 'input2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159603867522351370269'}, {'name': 'output1', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '1897672120697319566260'}, {'name': 'output2', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '143159614491512367170049'}]}}]"
