block_height,block_signed_at,block_hash,tx_hash,tx_offset,miner_address,from_address,to_address,value,gas_offered,gas_spent,gas_price,fees_paid,successful,chain_id,chain_name,explorers,from_address_label,to_address_label,gas_metadata,gas_quote_rate,gas_quote,pretty_gas_quote,value_quote,pretty_value_quote,log_events
137279590,2025-06-17 10:59:17+00:00,0x0485b70d4bd4c5b5daaf28764b4cc1ab01177085d5d54cf93e93f3300c13c72c,0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d,9,******************************************,******************************************,******************************************,100000000000000,885706,226288,1000700,257612060250,True,10,optimism-mainnet,"[{'label': 'Optimistic Etherscan', 'url': 'https://optimistic.etherscan.io/tx/0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1670347457-optimism-icon-white.svg'}",2554.5847964499326,0.0005784765347381557,$0.00,0.2554584796449933,$0.26,"[{'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 13, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xe1fffcc4923d04b559f4d29a8bfc6cda04eb5b0d3c460751c2402c5c5cc9109c', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000005af3107a4000', 'decoded': {'name': 'Deposit', 'signature': 'Deposit(indexed address dst, uint256 wad)', 'params': [{'name': 'dst', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100000000000000'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 14, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD₮0', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USD₮0', 'supports_erc': ['erc20', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000a9f987869bfe56d3a2ece2e79df55684d0bbcd8b', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000003e5e2', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '255458'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 15, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86', '0x000000000000000000000000a9f987869bfe56d3a2ece2e79df55684d0bbcd8b'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000005af3107a4000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100000000000000'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 16, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc1a1e00000000000000000000000000000000000000000000000000005af3107a40000000000000000000000000000000000000004d443c8fb4af2a53667036604d26000000000000000000000000000000000000000000000000018353a6ed0c951c00000000000000000000000000000000000000000000000000000000000304e2', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-255458'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '100000000000000'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '1567151797362387280219834912099622'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '109022792393725212'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '197858'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 17, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x2db5ddd0b42bdbca0d69ea16f234a870a485854ae0d91f16643d6f317d8b8994', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee', '0x00000000000000000000000001bff41798a0bcf287b996046ca68b395dbc1071'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae00000000000000000000000000000000000000000000000000005af3107a4000000000000000000000000000000000000000000000000000000000000003e0b3000000000000000000000000000000000000000000000000000000000003e5e2', 'decoded': {'name': 'Route', 'signature': 'Route(indexed address from, address to, indexed address tokenIn, indexed address tokenOut, uint256 amountIn, uint256 amountOutMin, uint256 amountOut)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'tokenIn', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'tokenOut', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amountIn', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100000000000000'}, {'name': 'amountOutMin', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '254131'}, {'name': 'amountOut', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '255458'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 18, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD₮0', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USD₮0', 'supports_erc': ['erc20', 'erc165'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000005c79a719c9fda77d76220734a7ee97963d488869'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000000003e5e2', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '255458'}]}}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 19, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0x48f9c8b5300d2f596e438f42ed354ff8deac29aa89da8406587fa89ec37208e20000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001bff41798a0bcf287b996046ca68b395dbc107100000000000000000000000000000000000000000000000000005af3107a4000000000000000000000000000000000000000000000000000000000000003e5e20000000000000000000000000000000000000000000000000000000068514a85'}, {'block_height': 137279590, 'block_signed_at': '2025-06-17T10:59:17Z', 'tx_offset': 9, 'tx_hash': '0xfba58c52b0b7b060ed298c3871efeb678e9e84503e6f9841ffaa336af5eb751d', 'log_offset': 20, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x38eee76fd911eabac79da7af16053e809be0e12c8637f156e77e1af309b99537', '0x48f9c8b5300d2f596e438f42ed354ff8deac29aa89da8406587fa89ec37208e2'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000001200000000000000000000000005c79a719c9fda77d76220734a7ee97963d488869000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001bff41798a0bcf287b996046ca68b395dbc107100000000000000000000000000000000000000000000000000005af3107a4000000000000000000000000000000000000000000000000000000000000003e5e2000000000000000000000000000000000000000000000000000000000000000f6a756d7065722e65786368616e67650000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a30783030303030303030303030303030303030303030303030303030303030303030303030303030303000000000000000000000000000000000000000000000', 'decoded': {'name': 'LiFiGenericSwapCompleted', 'signature': 'LiFiGenericSwapCompleted(indexed bytes32 transactionId, string integrator, string referrer, address receiver, address fromAssetId, address toAssetId, uint256 fromAmount, uint256 toAmount)', 'params': [{'name': 'transactionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'SPnItTANL1luQ49C7TVP+N6sKaqJ2oQGWH+onsNyCOI='}, {'name': 'integrator', 'type': 'string', 'indexed': False, 'decoded': True, 'value': 'jumper.exchange'}, {'name': 'referrer', 'type': 'string', 'indexed': False, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': 'receiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': 'toAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '100000000000000'}, {'name': 'toAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '255458'}]}}]"
137279580,2025-06-17 10:58:57+00:00,0x34e9698f5154a5eaddf6b62f5bb33d582eb384904c84038e32794e03476d5955,0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857,24,******************************************,******************************************,******************************************,0,487566,297404,100601,94530169263,True,10,optimism-mainnet,"[{'label': 'Optimistic Etherscan', 'url': 'https://optimistic.etherscan.io/tx/0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1670347457-optimism-icon-white.svg'}",2554.5847964499326,7.643097966615842e-05,$0.00,0.0,$0.00,"[{'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 60, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped liquid staked Ether 2.0', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'wstETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006f4229328c48f95e623dcbcf0702b49f060f31fe', '0x0000000000000000000000000a2854fbbd9b3ef66f17d47284e7f899b9509330'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000008e3a0e5998000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 61, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped liquid staked Ether 2.0', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'wstETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000000a2854fbbd9b3ef66f17d47284e7f899b9509330', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000008e3a0e5998000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 62, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped liquid staked Ether 2.0', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'wstETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5', '0x0000000000000000000000002b959ccb3c8fc1ed7769004742c197e28251027e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000008e3a0e5998000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 63, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000008e3a0e5998000fffffffffffffffffffffffffffffffffffffffffffffffffff54ab37d7c718a000000000000000000000000000000000000000118fb5cf3d02897e5c28460e9000000000000000000000000000000000000000000000e4da5d7746d523360810000000000000000000000000000000000000000000000000000000000000746', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-3014089978908278'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '86959682085974530656594845929'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '67545480201999798722689'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '1862'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 64, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped liquid staked Ether 2.0', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'wstETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5', '0x0000000000000000000000002b959ccb3c8fc1ed7769004742c197e28251027e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000000000000000000', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '0'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 65, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped liquid staked Ether 2.0', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'wstETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5', '0x0000000000000000000000002b959ccb3c8fc1ed7769004742c197e28251027e'], 'raw_log_data': '0x0000000000000000000000000000000000000000000000000008e3a0e5998000', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 66, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000002b959ccb3c8fc1ed7769004742c197e28251027e', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000ab54c82838e76', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3014089978908278'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 67, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65', '0x0000000000000000000000002691f337abeb0146f16441ca4f82f363275851d5'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000ab54c82838e76', 'decoded': {'name': 'Withdrawal', 'signature': 'Withdrawal(indexed address src, uint256 wad)', 'params': [{'name': 'src', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3014089978908278'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 68, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x76af224a143865a50b41496e1a73622698692c565c1214bc862f18e22d829c5e', '0x0000000000000000000000000a2854fbbd9b3ef66f17d47284e7f899b9509330', '0x0000000000000000000000001f32b1c2345538c0c6f582fcb022739c4a194ebb', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'], 'raw_log_data': '0x0000000000000000000000000a2854fbbd9b3ef66f17d47284e7f899b95093300000000000000000000000000000000000000000000000000008e3a0e59980000000000000000000000000000000000000000000000000000008e3a0e5998000000000000000000000000000000000000000000000000000000ab54c82838e76000000000000000000000000000000000000000000000000000a630f55a37325000000000000000000000000000000000000000000000000000ab54c82838e76000000000000000000000000ef53a4bd0e16ccc9116770a41c4bd3ad1147bd4f', 'decoded': {'name': 'Swapped', 'signature': 'Swapped(indexed address sender, indexed address srcToken, indexed address dstToken, address dstReceiver, uint256 amount, uint256 spentAmount, uint256 returnAmount, uint256 minReturnAmount, uint256 guaranteedAmount, address referrer)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'srcToken', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'dstToken', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'dstReceiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'amount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}, {'name': 'spentAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2502080000000000'}, {'name': 'returnAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3014089978908278'}, {'name': 'minReturnAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '2923667279541029'}, {'name': 'guaranteedAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '3014089978908278'}, {'name': 'referrer', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0xef53a4bd0e16ccc9116770a41c4bd3ad1147bd4f'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 69, 'sender_address': '0x45d047bfcb1055b9dd531ef9605e8f0b0dc285f3', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/0x45d047bfcb1055b9dd531ef9605e8f0b0dc285f3.png', 'raw_log_topics': ['0x3d0ce9bfc3ed7d6862dbb28b2dea94561fe714a1b4d019aa8af39730d1ad7c3d', '0x0000000000000000000000000a2854fbbd9b3ef66f17d47284e7f899b9509330'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000017fc826c07f7', 'decoded': {'name': 'SafeReceived', 'signature': 'SafeReceived(indexed address sender, uint256 value)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '26373287315447'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 24, 'tx_hash': '0x6c0148932c21bfb514a041ba48f7e2311bf36e77abd9e486ad59ea66e4d38857', 'log_offset': 70, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xbeee1e6e7fe307ddcf84b0a16137a4430ad5e2480fc4f4a8e250ab56ccd7630d', '0xcbf46790f422d91c0bc3f78784cac5d123211479f745ef4d632dfd71fd4d53f3', '0x0000000000000000000000006f4229328c48f95e623dcbcf0702b49f060f31fe'], 'raw_log_data': None, 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed string aggregatorId, indexed address sender)', 'params': [{'name': 'aggregatorId', 'type': 'string', 'indexed': True, 'decoded': True, 'value': 'y/RnkPQi2RwLw/eHhMrF0SMhFHn3Re9NYy39cf1NU/M='}, {'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}]}}]"
137279580,2025-06-17 10:58:57+00:00,0x34e9698f5154a5eaddf6b62f5bb33d582eb384904c84038e32794e03476d5955,0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70,6,******************************************,******************************************,******************************************,0,1026827,359137,1000800,405616692674,True,10,optimism-mainnet,"[{'label': 'Optimistic Etherscan', 'url': 'https://optimistic.etherscan.io/tx/0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70'}]",,,"{'contract_decimals': 18, 'contract_name': 'Ether', 'contract_ticker_symbol': 'ETH', 'contract_address': '******************************************', 'supports_erc': [], 'logo_url': 'https://www.datocms-assets.com/86369/1670347457-optimism-icon-white.svg'}",2554.5847964499326,0.0009181798767786736,$0.00,0.0,$0.00,"[{'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 14, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925', '0x0000000000000000000000003fd2c397b086202238707e11ed20c48cb659ca95', '0x0000000000000000000000006307119078556fc8ad77781dfc67df20d75fb4f9'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64', 'decoded': {'name': 'Approval', 'signature': 'Approval(indexed address owner, indexed address spender, uint256 value)', 'params': [{'name': 'owner', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'spender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 15, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000003fd2c397b086202238707e11ed20c48cb659ca95', '0x0000000000000000000000006307119078556fc8ad77781dfc67df20d75fb4f9'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 16, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000006307119078556fc8ad77781dfc67df20d75fb4f9', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 17, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 18, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x000000000000000000000000478946bcd4a5a22b316470f5486fafb928c0ba25', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000002dff9f0a2fded46', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '207158919804349766'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 19, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'USD Coin', 'sender_contract_decimals': 6, 'sender_contract_ticker_symbol': 'USDC', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86', '0x000000000000000000000000478946bcd4a5a22b316470f5486fafb928c0ba25'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64', 'decoded': {'name': 'Transfer', 'signature': 'Transfer(indexed address from, indexed address to, uint256 value)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'value', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 20, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0xc42079f94a6350d7e6235f29174924f928cc2ac818eb64fed8004e115fbcca67', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86'], 'raw_log_data': '0x000000000000000000000000000000000000000000000000000000001f943e64fffffffffffffffffffffffffffffffffffffffffffffffffd20060f5d0212ba0000000000000000000000000000000000004d421ac1e4cc846523217f7a92a70000000000000000000000000000000000000000000000000a56bd2f0085ea6900000000000000000000000000000000000000000000000000000000000304e0', 'decoded': {'name': 'Swap', 'signature': 'Swap(indexed address sender, indexed address recipient, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)', 'params': [{'name': 'sender', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'recipient', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amount0', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '529808996'}, {'name': 'amount1', 'type': 'int256', 'indexed': False, 'decoded': True, 'value': '-207158919804349766'}, {'name': 'sqrtPriceX96', 'type': 'uint160', 'indexed': False, 'decoded': True, 'value': '1566982879220530843475658103493287'}, {'name': 'liquidity', 'type': 'uint128', 'indexed': False, 'decoded': True, 'value': '744990797946284649'}, {'name': 'tick', 'type': 'int24', 'indexed': False, 'decoded': True, 'value': '197856'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 21, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': 'Wrapped Ether', 'sender_contract_decimals': 18, 'sender_contract_ticker_symbol': 'WETH', 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x7fcf532c15f0a6db0bd6d0e038bea71d30d808c7d98cb3bf7268a95bf5081b65', '0x0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e86'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000002dff9f0a2fded46', 'decoded': {'name': 'Withdrawal', 'signature': 'Withdrawal(indexed address src, uint256 wad)', 'params': [{'name': 'src', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'wad', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '207158919804349766'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 22, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': 0, 'sender_contract_ticker_symbol': None, 'supports_erc': [], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x2db5ddd0b42bdbca0d69ea16f234a870a485854ae0d91f16643d6f317d8b8994', '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae', '0x0000000000000000000000000b2c639c533813f4aa9d7837caf62653d097ff85', '0x000000000000000000000000eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee'], 'raw_log_data': '0x0000000000000000000000001231deb6f5749ef6ce6943a275a1d3e7486f4eae000000000000000000000000000000000000000000000000000000001f943e6400000000000000000000000000000000000000000000000002dc4ce7a345419300000000000000000000000000000000000000000000000002dff9f0a2fded46', 'decoded': {'name': 'Route', 'signature': 'Route(indexed address from, address to, indexed address tokenIn, indexed address tokenOut, uint256 amountIn, uint256 amountOutMin, uint256 amountOut)', 'params': [{'name': 'from', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'to', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'tokenIn', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'tokenOut', 'type': 'address', 'indexed': True, 'decoded': True, 'value': '******************************************'}, {'name': 'amountIn', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}, {'name': 'amountOutMin', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '206124240712581523'}, {'name': 'amountOut', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '207158919804349766'}]}}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 23, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x7bfdfdb5e3a3776976e53cb0607060f54c5312701c8cba1155cc4d5394440b38'], 'raw_log_data': '0xbd2534b12f496e7a3f75d5cbfd605537a5cdc617cb6194522414371c5312936d0000000000000000000000009caab1750104147c9872772b3d0be3d4290e1e860000000000000000000000000b2c639c533813f4aa9d7837caf62653d097ff850000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f943e6400000000000000000000000000000000000000000000000002dff9f0a2fded460000000000000000000000000000000000000000000000000000000068514a71'}, {'block_height': 137279580, 'block_signed_at': '2025-06-17T10:58:57Z', 'tx_offset': 6, 'tx_hash': '0x4c67088cb9863a6e5c91c2aeed22cda032b24f112806d234cf6cadd2e5bfcd70', 'log_offset': 24, 'sender_address': '******************************************', 'sender_address_label': None, 'sender_factory_address': None, 'sender_name': None, 'sender_contract_decimals': None, 'sender_contract_ticker_symbol': None, 'supports_erc': ['erc20'], 'sender_logo_url': 'https://logos.covalenthq.com/tokens/10/******************************************.png', 'raw_log_topics': ['0x38eee76fd911eabac79da7af16053e809be0e12c8637f156e77e1af309b99537', '0xbd2534b12f496e7a3f75d5cbfd605537a5cdc617cb6194522414371c5312936d'], 'raw_log_data': '0x00000000000000000000000000000000000000000000000000000000000000e000000000000000000000000000000000000000000000000000000000000001200000000000000000000000003fd2c397b086202238707e11ed20c48cb659ca950000000000000000000000000b2c639c533813f4aa9d7837caf62653d097ff850000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001f943e6400000000000000000000000000000000000000000000000002dff9f0a2fded46000000000000000000000000000000000000000000000000000000000000000f6a756d7065722e65786368616e67650000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a30783030303030303030303030303030303030303030303030303030303030303030303030303030303000000000000000000000000000000000000000000000', 'decoded': {'name': 'LiFiGenericSwapCompleted', 'signature': 'LiFiGenericSwapCompleted(indexed bytes32 transactionId, string integrator, string referrer, address receiver, address fromAssetId, address toAssetId, uint256 fromAmount, uint256 toAmount)', 'params': [{'name': 'transactionId', 'type': 'bytes32', 'indexed': True, 'decoded': True, 'value': 'vSU0sS9Jbno/ddXL/WBVN6XNxhfLYZRSJBQ3HFMSk20='}, {'name': 'integrator', 'type': 'string', 'indexed': False, 'decoded': True, 'value': 'jumper.exchange'}, {'name': 'referrer', 'type': 'string', 'indexed': False, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': 'receiver', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'fromAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '******************************************'}, {'name': 'toAssetId', 'type': 'address', 'indexed': False, 'decoded': True, 'value': '0x0000000000000000000000000000000000000000'}, {'name': 'fromAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '529808996'}, {'name': 'toAmount', 'type': 'uint256', 'indexed': False, 'decoded': True, 'value': '207158919804349766'}]}}]"
