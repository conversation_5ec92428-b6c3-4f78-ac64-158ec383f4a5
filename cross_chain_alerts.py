import os
import json
import requests
from datetime import datetime, timedelta, timezone
import pandas as pd
from typing import Dict, List, Any

class CrossChainAlerts:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.endpoint = "https://api.covalenthq.com/v1"
        self.headers = {
            "Content-Type": "application/json"
        }

        # Token contract addresses for different chains
        self.token_contracts = {
            "USDT": {
                "1": "******************************************",  # Ethereum
                # "TR7NHqJEKQXgTCi8q8ZY4pL8otSzgJjL6t", # Tron (TRC-20) - Covalent API may not support
                "137": "******************************************",  # Polygon
                "56": "******************************************",   # BSC
                "42161": "******************************************",  # Arbitrum
                "10": "******************************************",    # Optimism
                "43114": "******************************************",  # Avalanche
                "250": "******************************************",    # Fantom
                "100": "******************************************",     # Gnosis
                # "Es9VMFrzaCERMJfRFr4H2FYD4KCoNkY11McC8BenwNYB" # Solana (SPL) - Covalent API may not support
            },
            "USDC": {
                "1": "******************************************",  # Ethereum
                # "TEKXiTehnzSmSe2XqrBj4w32RUN966rdz8", # Tron (TRC-20) - Covalent API may not support
                "137": "******************************************",  # Polygon
                "56": "******************************************",   # BSC
                "42161": "******************************************",  # Arbitrum
                "10": "******************************************",    # Optimism
                "43114": "******************************************",  # Avalanche
                "250": "******************************************",    # Fantom
                "100": "******************************************",     # Gnosis
                # "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGKZWyTDT1v" # Solana (SPL) - Covalent API may not support
            },
            "ETH": {
                "1": "******************************************",  # Ethereum
                "137": "******************************************",  # Polygon
                "56": "******************************************",   # BSC
                "42161": "******************************************",  # Arbitrum
                "10": "******************************************",    # Optimism
                "43114": "******************************************",  # Avalanche
                "250": "******************************************",    # Fantom
                "100": "******************************************"     # Gnosis
            }
        }

    def fetch_cross_chain_data(self, date: str, token: str = "USDT") -> Dict[str, Any]:
        """
        Fetch cross-chain data for specified token across all supported chains.
        
        Args:
            date (str): Date in YYYY-MM-DD format
            token (str): Token symbol (USDT, USDC, or ETH)
            
        Returns:
            Dict[str, Any]: Cross-chain data including transactions
        """
        print(f"Fetching {token} data for date: {date}")

        if token not in self.token_contracts:
            print(f"Token {token} not supported")
            return {}

        all_transactions = {}
        # Create timezone-aware datetime objects
        target_date = datetime.strptime(date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        start_date = target_date
        end_date = target_date + timedelta(days=1)

        # Use the allchains endpoint for more efficient cross-chain querying
        url = f"{self.endpoint}/allchains/transactions/"
        
        # Get all contract addresses for the token
        addresses = list(self.token_contracts[token].values())
        print(f"Querying addresses: {addresses}")
        
        params = {
            "key": self.api_key,
            "addresses": ",".join(addresses),
            "with-decoded-logs": "true",
            "page-size": 1000,
            "starting-block": "0",
            "ending-block": "99999999"
        }

        try:
            print(f"Making API request to: {url}")
            response = requests.get(
                url,
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            
            data = response.json()
            print(f"API Response status: {response.status_code}")
            
            if data.get('data', {}).get('items'):
                items = data['data']['items']
                print(f"Total transactions found: {len(items)}")
                
                # Print first few transaction dates for debugging
                print("\nSample transaction dates:")
                for tx in items[:3]:
                    tx_date = datetime.fromisoformat(tx.get('block_signed_at', '').replace('Z', '+00:00'))
                    print(f"Transaction {tx.get('tx_hash')[:10]}...: {tx_date}")
                
                # Group transactions by chain
                for tx in items:
                    chain_id = str(tx.get('chain_id'))
                    if chain_id in self.token_contracts[token]:
                        if chain_id not in all_transactions:
                            all_transactions[chain_id] = []
                        
                        # Filter by date range
                        tx_date = datetime.fromisoformat(tx.get('block_signed_at', '').replace('Z', '+00:00'))
                        if start_date <= tx_date < end_date:
                            all_transactions[chain_id].append(tx)
                            print(f"Found matching transaction on chain {chain_id}: {tx.get('tx_hash')}")
                
                # Print summary
                for chain_id, txs in all_transactions.items():
                    print(f"Found {len(txs)} {token} transactions on chain {chain_id} for date {date}")
            else:
                print(f"No transactions found for {token}")
                if 'error' in data:
                    print(f"API Error: {data.get('error_message', 'Unknown error')}")

        except requests.exceptions.RequestException as e:
            print(f"Error fetching cross-chain data: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response text: {e.response.text}")

        return all_transactions

    def process_cross_chain_data(self, data: Dict[str, Any], token: str) -> Dict[str, pd.DataFrame]:
        """
        Process the cross-chain data into pandas DataFrames for each network.
        
        Args:
            data (Dict[str, Any]): Raw cross-chain data from the API
            token (str): Token symbol
            
        Returns:
            Dict[str, pd.DataFrame]: Dictionary of DataFrames for each network
        """
        if not data:
            print("No data received from API")
            return {}

        print("Processing data...")
        processed_data = {}
        
        chain_names = {
            "1": "ethereum",
            "137": "polygon",
            "56": "bsc",
            "42161": "arbitrum",
            "10": "optimism",
            "43114": "avalanche",
            "250": "fantom",
            "100": "gnosis"
        }
        
        for chain_id, transactions in data.items():
            if transactions:
                df = pd.DataFrame(transactions)
                # Convert timestamp to readable date
                if 'block_signed_at' in df.columns:
                    df['block_signed_at'] = pd.to_datetime(df['block_signed_at'])
                chain_name = chain_names.get(chain_id, f"chain_{chain_id}")
                processed_data[f"{chain_name}_{token.lower()}_transactions"] = df
                print(f"Processed {len(df)} {token} transactions for {chain_name}")

        return processed_data

    def save_to_csv(self, data: Dict[str, pd.DataFrame], output_dir: str = "cross_chain_data"):
        """
        Save the processed data to CSV files.
        
        Args:
            data (Dict[str, pd.DataFrame]): Dictionary of DataFrames to save
            output_dir (str): Directory to save the CSV files
        """
        if not data:
            print("No data to save")
            return

        if not os.path.exists(output_dir):
            print(f"Creating directory: {output_dir}")
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for name, df in data.items():
            filename = f"{name}_{timestamp}.csv"
            filepath = os.path.join(output_dir, filename)
            df.to_csv(filepath, index=False)
            print(f"Saved {filename} with {len(df)} rows")

def main():
    # Use the provided API key
    api_key = "cqt_rQWjhTpqf9YcBQ4kXC3TKwtXYmWd"
    
    print("Initializing CrossChainAlerts...")
    # Initialize the CrossChainAlerts class
    alerts = CrossChainAlerts(api_key)

    # Use current date
    target_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    print(f"Using date: {target_date}")

    # Fetch data for each token
    for token in ["USDT", "USDC", "ETH"]:
        print(f"\nProcessing {token}...")
        data = alerts.fetch_cross_chain_data(target_date, token)
        
        if data:
            # Calculate total transactions for the current token across all chains
            total_token_transactions = sum(len(txs) for txs in data.values())
            print(f"Total {token} transactions found across all chains for {target_date}: {total_token_transactions}")

            # Process the data
            processed_data = alerts.process_cross_chain_data(data, token)
            
            # Save to CSV files
            alerts.save_to_csv(processed_data)
        else:
            print(f"No {token} data was fetched from the API")

if __name__ == "__main__":
    main() 