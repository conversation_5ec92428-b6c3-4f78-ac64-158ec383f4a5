import requests
import pandas as pd
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Set
import os

# Bitquery GraphQL endpoint
BITQUERY_ENDPOINT = "https://streaming.bitquery.io/graphql"

# Your Bitquery API key
API_KEY = "ory_at_SFyh2AA3DYTBkY7hyX7h4X8gq6L5RHH2lBHDVBHbMpk.i0VwKGVvIpcXtdDWholZrkcro96ZHpkYFlVJBhoAzug"

# GraphQL query
# QUERY = """
# query GetAdvanceTransactions(
#     $network: evm_network!
#     $date: DateTime!
#     $limit: Int!
#     $usdt: String!
#     $usdc: String!
# ) {
#     EVM(network: $network, dataset: combined) {
#         # Native ETH transactions
#         Transactions(
#             limit: {count: $limit}
#             where: {
#                 Block: {Time: {since: $date}}
#             }
#         ) {
#             Block {
#                 Number
#                 Time
#             }
#             Transaction {
#                 Hash
#                 From
#                 To
#                 Value
#                 ValueInUSD
#                 Gas
#                 GasPrice
#                 Cost
#             }
#         }
        
#         # Token transfers (USDT and USDC)
#         Transfers(
#             limit: {count: $limit}
#             where: {
#                 Block: {Time: {since: $date}}
#                 Transfer: {
#                     Currency: {
#                         SmartContract: {in: [$usdt, $usdc]}
#                     }
#                 }
#             }
#         ) {
#             Block {
#                 Number
#                 Time
#             }
#             Transaction {
#                 Hash
#                 From
#                 To
#                 Gas
#                 GasPrice
#                 Cost
#             }
#             Transfer {
#                 Amount
#                 AmountInUSD
#                 Currency {
#                     Symbol
#                     Name
#                     SmartContract
#                 }
#                 Sender
#                 Receiver
#             }
#         }
#     }
# }
# """

# def fetch_data() -> Dict[str, Any]:
#     """
#     Fetch data from Bitquery GraphQL API
#     """
#     # Variables for the query
#     variables = {
#         "network": "eth",
#         "date": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%dT%H:%M:%SZ"),  # Format: YYYY-MM-DDThh:mm:ssZ
#         "limit": 100,  # Increased limit for more data
#         "usdt": "******************************************",  # USDT contract address
#         "usdc": "******************************************"   # USDC contract address
#     }

#     # Headers for the request
#     headers = {
#         "Content-Type": "application/json",
#         "Authorization": f"Bearer {API_KEY}"
#     }

#     try:
#         # Make the request
#         response = requests.post(
#             BITQUERY_ENDPOINT,
#             json={"query": QUERY, "variables": variables},
#             headers=headers
#         )

#         # Print response details for debugging
#         print(f"Response Status Code: {response.status_code}")
#         print(f"Response Headers: {response.headers}")
        
#         response_json = response.json()
#         print(f"Response JSON: {json.dumps(response_json, indent=2)}")

#         if response.status_code != 200:
#             error_message = response_json.get('errors', [{'message': 'Unknown error'}])[0]['message']
#             raise Exception(f"Query failed: {error_message}")

#         if 'errors' in response_json:
#             error_message = response_json['errors'][0]['message']
#             raise Exception(f"GraphQL error: {error_message}")

#         if 'data' not in response_json:
#             raise Exception("No data field in response")

#         return response_json

#     except requests.exceptions.RequestException as e:
#         raise Exception(f"Network error: {str(e)}")
#     except json.JSONDecodeError as e:
#         raise Exception(f"Invalid JSON response: {str(e)}")
#     except Exception as e:
#         raise Exception(f"Unexpected error: {str(e)}")

# def process_eth_transactions(data: List[Dict[str, Any]], address_stats: Dict[str, int]) -> pd.DataFrame:
#     """
#     Process Ethereum transactions into a DataFrame
#     """
#     processed_data = []
#     for tx in data:
#         transaction = tx['Transaction']
#         block = tx['Block']
#         gas_cost = float(transaction['GasPrice']) * float(transaction['Gas'])
        
#         # Get address transaction counts
#         sender_count = address_stats.get(transaction['From'], 0)
#         receiver_count = address_stats.get(transaction['To'], 0)
        
#         processed_data.append({
#             'type': 'ETH',
#             'hash': transaction['Hash'],
#             'sender': transaction['From'],
#             'sender_tx_count': sender_count,
#             'receiver': transaction['To'],
#             'receiver_tx_count': receiver_count,
#             'block_height': block['Number'],
#             'timestamp': block['Time'],
#             'amount': transaction['Value'],
#             'amount_usd': transaction.get('ValueInUSD', '0'),
#             'gas_price': transaction['GasPrice'],
#             'gas_used': transaction['Gas'],
#             'gas_cost': gas_cost,
#             'total_cost': transaction['Cost']
#         })
#     return pd.DataFrame(processed_data)

# def process_token_transfers(data: List[Dict[str, Any]], address_stats: Dict[str, int]) -> pd.DataFrame:
#     """
#     Process token transfers into a DataFrame
#     """
#     processed_data = []
#     for transfer in data:
#         tx = transfer['Transaction']
#         block = transfer['Block']
#         transfer_info = transfer['Transfer']
#         gas_cost = float(tx['GasPrice']) * float(tx['Gas'])
        
#         # Get USD amount, defaulting to 0 if not available
#         amount_usd = transfer_info.get('AmountInUSD', '0')
#         if amount_usd == '0' and transfer_info['Currency']['Symbol'] in ['USDC', 'USDT']:
#             amount_usd = transfer_info['Amount']
        
#         # Get address transaction counts
#         sender_count = address_stats.get(transfer_info['Sender'], 0)
#         receiver_count = address_stats.get(transfer_info['Receiver'], 0)
        
#         processed_data.append({
#             'type': transfer_info['Currency']['Symbol'],
#             'hash': tx['Hash'],
#             'sender': transfer_info['Sender'],
#             'sender_tx_count': sender_count,
#             'receiver': transfer_info['Receiver'],
#             'receiver_tx_count': receiver_count,
#             'block_height': block['Number'],
#             'timestamp': block['Time'],
#             'amount': transfer_info['Amount'],
#             'amount_usd': amount_usd,
#             'gas_price': tx['GasPrice'],
#             'gas_used': tx['Gas'],
#             'gas_cost': gas_cost,
#             'total_cost': tx['Cost'],
#             'token_address': transfer_info['Currency']['SmartContract'],
#             'token_name': transfer_info['Currency']['Name']
#         })
#     return pd.DataFrame(processed_data)

# def calculate_address_stats(df: pd.DataFrame) -> Dict[str, int]:
#     """
#     Calculate transaction count for each unique address
#     """
#     address_stats = {}
    
#     # Get all unique addresses
#     all_addresses = set(df['sender'].unique()) | set(df['receiver'].unique())
    
#     # Calculate transaction count for each address
#     for addr in all_addresses:
#         # Count transactions where address is either sender or receiver
#         count = len(df[(df['sender'] == addr) | (df['receiver'] == addr)])
#         address_stats[addr] = count
    
#     return address_stats

# def save_to_csv(df: pd.DataFrame, filename: str):
#     """
#     Save DataFrame to CSV file
#     """
#     # Create data directory if it doesn't exist
#     os.makedirs('data', exist_ok=True)
    
#     # Save to CSV
#     filepath = os.path.join('data', filename)
#     df.to_csv(filepath, index=False)
#     print(f"Data saved to {filepath}")

def main():
    test = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%dT%H:%M:%SZ")
    print(test)
    # try:
    #     # Fetch data
    #     print("Fetching data from Bitquery...")
    #     response_data = fetch_data()
        
    #     # Extract data from response
    #     if not response_data.get('data', {}).get('EVM'):
    #         raise Exception("No EVM data in response")
            
    #     eth_data = response_data['data']['EVM'].get('Transactions', [])
    #     token_data = response_data['data']['EVM'].get('Transfers', [])
        
    #     if not eth_data and not token_data:
    #         print("No transactions found in the specified time range")
    #         return
        
    #     # Process data
    #     print("Processing data...")
        
    #     # First process without address stats to get initial data
    #     initial_eth_df = process_eth_transactions(eth_data, {})
    #     initial_token_df = process_token_transfers(token_data, {})
    #     initial_combined_df = pd.concat([initial_eth_df, initial_token_df], ignore_index=True)
        
    #     # Calculate address statistics
    #     print("Calculating address statistics...")
    #     address_stats = calculate_address_stats(initial_combined_df)
        
    #     # Process again with address stats
    #     eth_df = process_eth_transactions(eth_data, address_stats)
    #     token_df = process_token_transfers(token_data, address_stats)
        
    #     # Combine dataframes
    #     combined_df = pd.concat([eth_df, token_df], ignore_index=True)
        
    #     # Sort by timestamp
    #     combined_df = combined_df.sort_values('timestamp', ascending=False)
        
    #     # Save to CSV
    #     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    #     save_to_csv(combined_df, f'advance_transactions_{timestamp}.csv')
        
    #     print(f"Total transactions processed: {len(combined_df)}")
    #     print(f"ETH transactions: {len(eth_df)}")
    #     print(f"Token transfers: {len(token_df)}")
    #     print(f"Unique addresses found: {len(address_stats)}")
        
    # except Exception as e:
    #     print(f"An error occurred: {str(e)}")
    #     print("Full error details:", e.__class__.__name__)

if __name__ == "__main__":
    main() 