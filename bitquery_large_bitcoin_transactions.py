#!/usr/bin/env python3
"""
BitQuery.io Python Script for Large Bitcoin Transactions
This script queries BitQuery API for Bitcoin transactions with high output values.
"""

import requests
import json
import os
from datetime import datetime
from typing import Dict, Any, Optional


class BitQueryClient:
    """Client for interacting with BitQuery GraphQL API"""
    
    def __init__(self, api_key: str):
        """
        Initialize BitQuery client
        
        Args:
            api_key (str): Your BitQuery API key
        """
        self.api_key = api_key
        self.base_url = "https://graphql.bitquery.io"
        self.headers = {
            "Content-Type": "application/json",
            "X-API-KEY": self.api_key
        }
    
    def execute_query(self, query: str, variables: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a GraphQL query
        
        Args:
            query (str): GraphQL query string
            variables (dict, optional): Query variables
            
        Returns:
            dict: API response
        """
        payload = {
            "query": query,
            "variables": variables or {}
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"Error making request: {e}")
            raise
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON response: {e}")
            raise


def get_large_bitcoin_transactions(
    client: BitQueryClient,
    start_date: str = "2025-06-01",
    end_date: str = "2025-06-10",
    min_value_usd: str = "50000000",
    limit: int = 100
) -> Dict[str, Any]:
    """
    Query for large Bitcoin transactions
    
    Args:
        client (BitQueryClient): BitQuery client instance
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
        min_value_usd (str): Minimum transaction value in USD
        limit (int): Maximum number of transactions to return
        
    Returns:
        dict: Query results
    """
    
    query = """
    query LargeBitcoinTransactions($startDate: ISO8601DateTime, $endDate: ISO8601DateTime, $minValue: String, $limit: Int) {
      bitcoin(network: bitcoin) {
        transactions(
          options: {
            limit: $limit,
            desc: "block.timestamp.iso8601"
          }
          date: {
            since: $startDate,
            till: $endDate
          }
          outputValue: {
            gt: $minValue
          }
        ) {
          count
          block {
            timestamp {
              iso8601
            }
            height
          }
          hash
          outputValue(in: USD)
          inputValue(in: USD)
          feeValue(in: USD)
          outputCount
          inputCount
        }
      }
    }
    """
    
    variables = {
        "startDate": start_date,
        "endDate": end_date,
        "minValue": min_value_usd,
        "limit": limit
    }
    
    return client.execute_query(query, variables)


def format_transaction_output(transactions_data: Dict[str, Any]) -> None:
    """
    Format and print transaction data
    
    Args:
        transactions_data (dict): Transaction data from API response
    """
    if "errors" in transactions_data:
        print("❌ API Errors:")
        for error in transactions_data["errors"]:
            print(f"  - {error.get('message', 'Unknown error')}")
        return
    
    try:
        transactions = transactions_data["data"]["bitcoin"]["transactions"]
        
        print(f"📊 Large Bitcoin Transactions Summary")
        print(f"{'='*50}")
        print(f"Total transactions found: {len(transactions)}")
        
        if not transactions:
            print("No transactions found matching the criteria.")
            return
        
        print(f"\n🔍 Transaction Details:")
        print(f"{'='*80}")
        
        for i, tx in enumerate(transactions, 1):
            print(f"\n{i}. Transaction Hash: {tx['hash']}")
            print(f"   📅 Timestamp: {tx['block']['timestamp']['iso8601']}")
            print(f"   🏗️  Block Height: {tx['block']['height']}")
            print(f"   💰 Output Value: ${tx['outputValue']:,.2f} USD")
            print(f"   📥 Input Value: ${tx['inputValue']:,.2f} USD")
            print(f"   💸 Fee: ${tx['feeValue']:,.2f} USD")
            print(f"   🔢 Outputs: {tx['outputCount']} | Inputs: {tx['inputCount']}")
            
    except KeyError as e:
        print(f"❌ Error parsing response data: {e}")
        print("Raw response:", json.dumps(transactions_data, indent=2))


def main():
    """Main function to execute the script"""
    
    # Get API key from environment variable or user input
    api_key = os.getenv("BITQUERY_API_KEY")
    
    if not api_key:
        api_key = input("Please enter your BitQuery API key: ").strip()
        if not api_key:
            print("❌ API key is required!")
            return
    
    # Initialize client
    client = BitQueryClient(api_key)
    
    # Configuration
    start_date = "2024-01-01"  # Updated to a more recent date
    end_date = "2024-01-31"
    min_value_usd = "1000000"  # $1M USD minimum (adjusted for realistic data)
    limit = 20
    
    print(f"🚀 Querying BitQuery for large Bitcoin transactions...")
    print(f"📅 Date range: {start_date} to {end_date}")
    print(f"💰 Minimum value: ${int(min_value_usd):,} USD")
    print(f"📊 Limit: {limit} transactions")
    print(f"{'='*60}")
    
    try:
        # Execute query
        result = get_large_bitcoin_transactions(
            client=client,
            start_date=start_date,
            end_date=end_date,
            min_value_usd=min_value_usd,
            limit=limit
        )
        
        # Format and display results
        format_transaction_output(result)
        
    except Exception as e:
        print(f"❌ Error executing query: {e}")


if __name__ == "__main__":
    main()
